/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.model.OrderStatusCalculationResult;
import com.fulfillmen.shop.domain.service.OrderStatusCalculationService;
import com.fulfillmen.shop.domain.service.OrderStatusCalculator;
import com.fulfillmen.shop.manager.core.order.OrderStatusSyncService;
import com.fulfillmen.shop.manager.core.order.event.OrderStatusChangeEvent;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

/**
 * 订单状态管理集成测试
 *
 * <pre>
 * 测试整个状态管理系统的集成功能：
 * 1. 状态计算器与同步服务的集成
 * 2. 事件发布与监听的集成
 * 3. 级联状态更新的完整流程
 * 4. 并发场景下的状态一致性
 * 5. 异常情况的处理机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态管理集成测试
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class OrderStatusIntegrationTest {

    @Mock
    private ApplicationEventPublisher eventPublisher;

    private OrderStatusCalculator statusCalculator;
    private OrderStatusCalculationService calculationService;
    private OrderStatusSyncService syncService;

    @BeforeEach
    void setUp() {
        statusCalculator = new OrderStatusCalculator();
        calculationService = new OrderStatusCalculationService(statusCalculator);
        syncService = new OrderStatusSyncService(calculationService, eventPublisher);
    }

    @Test
    @DisplayName("测试完整的状态流转场景 - 从订单项到采购订单")
    void testCompleteStatusFlowIntegration() {
        // 场景：订单项状态变化 -> 供应商订单状态变化 -> 采购订单状态变化

        // 1. 测试订单项状态计算影响供应商订单状态
        List<TzOrderItemStatusEnum> itemStatuses = Arrays.asList(
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED
        );

        TzOrderSupplierStatusEnum supplierStatus = statusCalculator.calculateSupplierOrderStatus(itemStatuses);
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, supplierStatus);

        // 2. 测试供应商订单状态计算影响采购订单状态
        List<TzOrderSupplierStatusEnum> supplierStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED
        );

        TzOrderPurchaseStatusEnum purchaseStatus = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, purchaseStatus);

        // 3. 验证完整的状态计算结果
        OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            supplierStatuses
        );

        assertTrue(result.isStatusChanged());
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, result.getNewStatus());
        assertEquals(100.0, result.getCompletionPercentage());
        assertTrue(result.isFinalStatus());
    }

    @Test
    @DisplayName("测试部分完成场景的状态计算")
    void testPartialCompletionScenario() {
        // 场景：部分供应商订单完成，部分还在处理中

        List<TzOrderSupplierStatusEnum> mixedStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS
        );

        OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            mixedStatuses
        );

        // 验证部分履约状态
        assertTrue(result.isStatusChanged());
        assertEquals(TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED, result.getNewStatus());
        assertEquals(3, result.getTotalSupplierOrders());
        assertEquals(1, result.getCompletedSupplierOrders());
        assertEquals(33.33333333333333, result.getCompletionPercentage(), 0.001);
        assertFalse(result.isFinalStatus());
    }

    @Test
    @DisplayName("测试状态回退场景")
    void testStatusRegressionScenario() {
        // 场景：某些异常情况导致状态需要回退

        // 测试从已发货回退到采购中（例如发货失败）
        List<TzOrderSupplierStatusEnum> regressedStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS
        );

        OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            regressedStatuses
        );

        assertTrue(result.isStatusChanged());
        assertEquals(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS, result.getNewStatus());
        assertFalse(result.isFinalStatus());
    }

    @Test
    @DisplayName("测试订单取消场景")
    void testOrderCancellationScenario() {
        // 场景：所有供应商订单都被取消

        List<TzOrderSupplierStatusEnum> cancelledStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.CANCELLED,
            TzOrderSupplierStatusEnum.CANCELLED,
            TzOrderSupplierStatusEnum.CANCELLED
        );

        OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            cancelledStatuses
        );

        assertTrue(result.isStatusChanged());
        assertEquals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED, result.getNewStatus());
        assertTrue(result.isFinalStatus());
        assertTrue(result.isShouldTriggerEvent());
    }

    @Test
    @DisplayName("测试事件发布集成")
    void testEventPublishingIntegration() {
        // 模拟状态同步服务的事件发布

        List<TzOrderSupplierStatusEnum> supplierStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED
        );

        OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            supplierStatuses
        );

        // 验证事件发布逻辑
        if (result.isShouldTriggerEvent()) {
            // 捕获事件发布
            ArgumentCaptor<OrderStatusChangeEvent> eventCaptor = ArgumentCaptor.forClass(OrderStatusChangeEvent.class);

            // 模拟同步服务中的事件发布
            OrderStatusChangeEvent event = new OrderStatusChangeEvent(
                12345L,
                result.getOriginalStatus(),
                result.getNewStatus(),
                result.getCalculationReason()
            );

            // 验证事件属性
            assertEquals(12345L, event.getPurchaseOrderId());
            assertEquals(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED, event.getOriginalStatus());
            assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, event.getNewStatus());
            assertEquals(OrderStatusChangeEvent.EventType.ORDER_COMPLETED, event.getEventType());
            assertTrue(event.isCriticalStatusChange());
            assertTrue(event.shouldNotifyUser());
        }
    }

    @Test
    @DisplayName("测试状态计算的幂等性")
    void testStatusCalculationIdempotency() {
        // 场景：相同的输入应该产生相同的输出

        List<TzOrderSupplierStatusEnum> supplierStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE,
            TzOrderSupplierStatusEnum.COMPLETED
        );

        // 多次计算应该得到相同结果
        TzOrderPurchaseStatusEnum result1 = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
        TzOrderPurchaseStatusEnum result2 = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
        TzOrderPurchaseStatusEnum result3 = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);

        assertEquals(result1, result2);
        assertEquals(result2, result3);
        assertEquals(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED, result1);
    }

    @Test
    @DisplayName("测试边界条件处理")
    void testBoundaryConditions() {
        // 测试空列表
        OrderStatusCalculationResult emptyResult = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.PAYMENT_PENDING,
            Arrays.asList()
        );
        assertFalse(emptyResult.isStatusChanged());
        assertEquals(0, emptyResult.getTotalSupplierOrders());

        // 测试单个供应商订单
        OrderStatusCalculationResult singleResult = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED,
            Arrays.asList(TzOrderSupplierStatusEnum.COMPLETED)
        );
        assertTrue(singleResult.isStatusChanged());
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, singleResult.getNewStatus());
        assertEquals(100.0, singleResult.getCompletionPercentage());
    }

    @Test
    @DisplayName("测试状态流转验证")
    void testStatusTransitionValidation() {
        // 测试合法的状态流转
        assertTrue(statusCalculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.PAYMENT_PENDING,
            TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED));

        assertTrue(statusCalculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED));

        // 测试非法的状态流转
        assertFalse(statusCalculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.IN_STOCK,
            TzOrderPurchaseStatusEnum.PAYMENT_PENDING));

        assertFalse(statusCalculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.ORDER_CANCELLED,
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS));

        // 测试取消状态的特殊情况
        assertTrue(statusCalculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            TzOrderPurchaseStatusEnum.ORDER_CANCELLED));
    }

    @Test
    @DisplayName("测试复杂业务场景")
    void testComplexBusinessScenario() {
        // 场景：大订单，多供应商，不同进度

        List<TzOrderSupplierStatusEnum> complexStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,           // 供应商1：已完成
            TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE, // 供应商2：已送达仓库
            TzOrderSupplierStatusEnum.SHIPPED,            // 供应商3：已发货
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS, // 供应商4：采购中
            TzOrderSupplierStatusEnum.PAID                // 供应商5：已支付
        );

        OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            complexStatuses
        );

        // 验证复杂场景的计算结果
        assertTrue(result.isStatusChanged());
        assertEquals(TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED, result.getNewStatus());
        assertEquals(5, result.getTotalSupplierOrders());
        assertEquals(1, result.getCompletedSupplierOrders());
        assertEquals(20.0, result.getCompletionPercentage());
        assertFalse(result.isFinalStatus());
        assertTrue(result.isShouldTriggerEvent());
    }
}
