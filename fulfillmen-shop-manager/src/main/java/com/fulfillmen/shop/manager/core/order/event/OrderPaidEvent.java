/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单支付事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当订单支付完成时触发此事件
 * @since 1.0.0
 */
@Getter
public class OrderPaidEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_PAID;
    private final Long orderId;
    private final BigDecimal paymentAmount;
    private final String paymentMethod;
    private final String transactionId;
    private final String tenantId;
    private final String userId;
    private final LocalDateTime eventTime;

    public OrderPaidEvent(Long orderId, BigDecimal paymentAmount, String paymentMethod,
        String transactionId, String tenantId, String userId) {
        super(orderId);
        this.orderId = orderId;
        this.paymentAmount = paymentAmount;
        this.paymentMethod = paymentMethod;
        this.transactionId = transactionId;
        this.tenantId = tenantId;
        this.userId = userId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("OrderPaidEvent{orderId=%d, amount=%s, method='%s', transactionId='%s', time=%s}",
            orderId, paymentAmount, paymentMethod, transactionId, eventTime);
    }
}