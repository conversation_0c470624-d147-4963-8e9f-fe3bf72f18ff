/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订单状态流转事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 订单状态流转事件，支持批量状态变更
 * @since 1.0.0
 */
@Getter
public class OrderStatusFlowEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_STATUS_CHANGED;
    private final Long orderId;
    private final OrderStatusFlow statusFlow;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public OrderStatusFlowEvent(Long orderId, OrderStatusFlow statusFlow, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.statusFlow = statusFlow;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("OrderStatusFlowEvent{orderId=%d, flowId='%s', stepsCount=%d, time=%s}",
            orderId, statusFlow.getFlowId(),
            statusFlow.getSteps() != null ? statusFlow.getSteps().size() : 0, eventTime);
    }
}