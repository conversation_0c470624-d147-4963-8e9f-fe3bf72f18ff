/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;

/**
 * 订单事件类型枚举
 *
 * <AUTHOR>
 * @date 2025/7/9 15:46
 * @description 定义系统中所有订单相关的事件类型，用于事件驱动架构
 * @since 1.0.0
 */
@Getter
public enum OrderEventTypeEnums {

    // ========== 订单生命周期事件 ==========
    /**
     * 订单创建事件 - 当采购订单创建完成时触发
     */
    ORDER_CREATED("订单创建"),

    /**
     * 订单支付事件 - 当订单支付完成时触发
     */
    ORDER_PAID("订单支付"),

    /**
     * 订单取消事件 - 当订单被取消时触发
     */
    ORDER_CANCELLED("订单取消"),

    /**
     * 订单完成事件 - 当订单履约完成时触发
     */
    ORDER_COMPLETED("订单完成"),

    // ========== 订单状态变更事件 ==========
    /**
     * 订单状态变更事件 - 当订单状态发生变化时触发
     */
    ORDER_STATUS_CHANGED("订单状态变更"),

    // ========== 采购相关事件 ==========
    /**
     * 采购开始事件 - 当开始向供应商采购时触发
     */
    PROCUREMENT_STARTED("采购开始"),

    /**
     * 采购完成事件 - 当供应商订单创建完成时触发
     */
    PROCUREMENT_COMPLETED("采购完成"),

    /**
     * 采购失败事件 - 当供应商订单创建失败时触发
     */
    PROCUREMENT_FAILED("采购失败"),

    // ========== 供应商订单事件 ==========
    /**
     * 供应商订单创建事件 - 当供应商订单创建时触发
     */
    SUPPLIER_ORDER_CREATED("供应商订单创建"),

    /**
     * 供应商订单更新事件 - 当供应商订单状态更新时触发
     */
    SUPPLIER_ORDER_UPDATED("供应商订单更新"),

    // ========== 物流相关事件 ==========
    /**
     * 商品发货事件 - 当商品从供应商发货时触发
     */
    GOODS_SHIPPED("商品发货"),

    /**
     * 商品到达仓库事件 - 当商品到达仓库时触发
     */
    GOODS_ARRIVED_WAREHOUSE("商品到达仓库"),

    /**
     * 商品入库事件 - 当商品完成入库时触发
     */
    GOODS_STOCKED("商品入库"),

    // ========== 异常处理事件 ==========
    /**
     * 订单异常事件 - 当订单处理过程中出现异常时触发
     */
    ORDER_EXCEPTION("订单异常"),

    /**
     * 支付异常事件 - 当支付过程中出现异常时触发
     */
    PAYMENT_EXCEPTION("支付异常"),

    /**
     * 库存不足事件 - 当库存不足时触发
     */
    INVENTORY_INSUFFICIENT("库存不足");

    private final String description;

    OrderEventTypeEnums(String description) {
        this.description = description;
    }

    /**
     * 判断是否为订单生命周期事件
     */
    public boolean isOrderLifecycleEvent() {
        return this == ORDER_CREATED || this == ORDER_PAID ||
            this == ORDER_CANCELLED || this == ORDER_COMPLETED;
    }

    /**
     * 判断是否为采购相关事件
     */
    public boolean isProcurementEvent() {
        return this == PROCUREMENT_STARTED || this == PROCUREMENT_COMPLETED ||
            this == PROCUREMENT_FAILED;
    }

    /**
     * 判断是否为异常事件
     */
    public boolean isExceptionEvent() {
        return this == ORDER_EXCEPTION || this == PAYMENT_EXCEPTION ||
            this == INVENTORY_INSUFFICIENT;
    }
}
