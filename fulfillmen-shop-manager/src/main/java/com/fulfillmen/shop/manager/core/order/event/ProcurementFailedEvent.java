/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 采购失败事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当供应商订单创建失败时触发此事件
 * @since 1.0.0
 */
@Getter
public class ProcurementFailedEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.PROCUREMENT_FAILED;
    private final Long orderId;
    private final String failureReason;
    private final String errorDetails;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public ProcurementFailedEvent(Long orderId, String failureReason,
        String errorDetails, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.failureReason = failureReason;
        this.errorDetails = errorDetails;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("ProcurementFailedEvent{orderId=%d, reason='%s', time=%s}",
            orderId, failureReason, eventTime);
    }
}