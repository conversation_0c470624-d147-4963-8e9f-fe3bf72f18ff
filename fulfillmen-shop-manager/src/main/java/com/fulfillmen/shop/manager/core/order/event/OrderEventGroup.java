/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Builder;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单事件组合
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 用于同时发布多个相关的订单事件
 * @since 1.0.0
 */
@Getter
@Builder
public class OrderEventGroup {

    /**
     * 事件组ID
     */
    private String groupId;

    /**
     * 主订单ID
     */
    private Long primaryOrderId;

    /**
     * 事件列表
     */
    private List<ApplicationEvent> events;

    /**
     * 事件组类型
     */
    private GroupType groupType;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 执行策略
     */
    private ExecutionStrategy executionStrategy;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 事件组类型枚举
     */
    public enum GroupType {

        ORDER_LIFECYCLE("订单生命周期事件组"),
        PROCUREMENT_PROCESS("采购流程事件组"),
        LOGISTICS_PROCESS("物流流程事件组"),
        EXCEPTION_HANDLING("异常处理事件组"),
        BUSINESS_WORKFLOW("业务流程事件组");

        private final String description;

        GroupType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 执行策略枚举
     */
    public enum ExecutionStrategy {

        PARALLEL("并行执行"),
        SEQUENTIAL("顺序执行"),
        CONDITIONAL("条件执行"),
        BEST_EFFORT("尽力执行");

        private final String description;

        ExecutionStrategy(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建订单生命周期事件组
     */
    public static OrderEventGroup createOrderLifecycleGroup(Long orderId, List<ApplicationEvent> events, String tenantId) {
        return OrderEventGroup.builder()
            .groupId(generateGroupId("LIFECYCLE", orderId))
            .primaryOrderId(orderId)
            .events(events)
            .groupType(GroupType.ORDER_LIFECYCLE)
            .tenantId(tenantId)
            .createTime(LocalDateTime.now())
            .executionStrategy(ExecutionStrategy.SEQUENTIAL)
            .build();
    }

    /**
     * 创建采购流程事件组
     */
    public static OrderEventGroup createProcurementProcessGroup(Long orderId, List<ApplicationEvent> events, String tenantId) {
        return OrderEventGroup.builder()
            .groupId(generateGroupId("PROCUREMENT", orderId))
            .primaryOrderId(orderId)
            .events(events)
            .groupType(GroupType.PROCUREMENT_PROCESS)
            .tenantId(tenantId)
            .createTime(LocalDateTime.now())
            .executionStrategy(ExecutionStrategy.PARALLEL)
            .build();
    }

    /**
     * 创建异常处理事件组
     */
    public static OrderEventGroup createExceptionHandlingGroup(Long orderId, List<ApplicationEvent> events, String tenantId) {
        return OrderEventGroup.builder()
            .groupId(generateGroupId("EXCEPTION", orderId))
            .primaryOrderId(orderId)
            .events(events)
            .groupType(GroupType.EXCEPTION_HANDLING)
            .tenantId(tenantId)
            .createTime(LocalDateTime.now())
            .executionStrategy(ExecutionStrategy.BEST_EFFORT)
            .build();
    }

    /**
     * 生成事件组ID
     */
    private static String generateGroupId(String prefix, Long orderId) {
        return String.format("%s-%d-%d", prefix, orderId, System.currentTimeMillis());
    }

    @Override
    public String toString() {
        return String.format("OrderEventGroup{groupId='%s', orderId=%d, eventCount=%d, type=%s, strategy=%s}",
            groupId, primaryOrderId, events != null ? events.size() : 0,
            groupType, executionStrategy);
    }
}