/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订单取消事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当订单被取消时触发此事件
 * @since 1.0.0
 */
@Getter
public class OrderCancelledEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_CANCELLED;
    private final Long orderId;
    private final String cancelReason;
    private final String cancelledBy;
    private final String tenantId;
    private final String userId;
    private final LocalDateTime eventTime;

    public OrderCancelledEvent(Long orderId, String cancelReason, String cancelledBy,
        String tenantId, String userId) {
        super(orderId);
        this.orderId = orderId;
        this.cancelReason = cancelReason;
        this.cancelledBy = cancelledBy;
        this.tenantId = tenantId;
        this.userId = userId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("OrderCancelledEvent{orderId=%d, reason='%s', cancelledBy='%s', time=%s}",
            orderId, cancelReason, cancelledBy, eventTime);
    }
}