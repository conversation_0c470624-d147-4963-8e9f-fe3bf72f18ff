/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 商品发货事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当商品从供应商发货时触发此事件
 * @since 1.0.0
 */
@Getter
public class GoodsShippedEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.GOODS_SHIPPED;
    private final Long orderId;
    private final String trackingNumber;
    private final String carrierName;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public GoodsShippedEvent(Long orderId, String trackingNumber, String carrierName, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.trackingNumber = trackingNumber;
        this.carrierName = carrierName;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("GoodsShippedEvent{orderId=%d, trackingNumber='%s', carrier='%s', time=%s}",
            orderId, trackingNumber, carrierName, eventTime);
    }
}