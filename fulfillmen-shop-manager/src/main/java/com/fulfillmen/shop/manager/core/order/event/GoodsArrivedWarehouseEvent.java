/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 商品到达仓库事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当商品到达仓库时触发此事件
 * @since 1.0.0
 */
@Getter
public class GoodsArrivedWarehouseEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.GOODS_ARRIVED_WAREHOUSE;
    private final Long orderId;
    private final String warehouseId;
    private final Integer receivedQuantity;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public GoodsArrivedWarehouseEvent(Long orderId, String warehouseId,
        Integer receivedQuantity, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.warehouseId = warehouseId;
        this.receivedQuantity = receivedQuantity;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("GoodsArrivedWarehouseEvent{orderId=%d, warehouseId='%s', quantity=%d, time=%s}",
            orderId, warehouseId, receivedQuantity, eventTime);
    }
}