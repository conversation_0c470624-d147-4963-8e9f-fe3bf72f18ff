/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订单完成事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当订单履约完成时触发此事件
 * @since 1.0.0
 */
@Getter
public class OrderCompletedEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_COMPLETED;
    private final Long orderId;
    private final String completionNote;
    private final String tenantId;
    private final String userId;
    private final LocalDateTime eventTime;

    public OrderCompletedEvent(Long orderId, String completionNote, String tenantId, String userId) {
        super(orderId);
        this.orderId = orderId;
        this.completionNote = completionNote;
        this.tenantId = tenantId;
        this.userId = userId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("OrderCompletedEvent{orderId=%d, note='%s', time=%s}",
            orderId, completionNote, eventTime);
    }
}