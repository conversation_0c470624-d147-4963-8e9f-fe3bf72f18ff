/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 商品入库事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当商品完成入库时触发此事件
 * @since 1.0.0
 */
@Getter
public class GoodsStockedEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.GOODS_STOCKED;
    private final Long orderId;
    private final Integer stockedQuantity;
    private final String qualityCheckResult;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public GoodsStockedEvent(Long orderId, Integer stockedQuantity,
        String qualityCheckResult, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.stockedQuantity = stockedQuantity;
        this.qualityCheckResult = qualityCheckResult;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("GoodsStockedEvent{orderId=%d, quantity=%d, qualityResult='%s', time=%s}",
            orderId, stockedQuantity, qualityCheckResult, eventTime);
    }
}