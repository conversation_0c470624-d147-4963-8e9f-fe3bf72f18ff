/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository.impl;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.fulfillmen.shop.dao.mapper.TzProductSkuMapper;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.manager.core.repository.TzProductSkuRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/7/7 10:08
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Repository
public class TzProductSkuRepositoryImpl extends CrudRepository<TzProductSkuMapper, TzProductSku> implements TzProductSkuRepository {

    // 批量新增
    @Override
    public boolean batchInsertSkus(List<TzProductSku> skus) {
        return this.baseMapper.batchInsertSkus(skus);
    }
}
