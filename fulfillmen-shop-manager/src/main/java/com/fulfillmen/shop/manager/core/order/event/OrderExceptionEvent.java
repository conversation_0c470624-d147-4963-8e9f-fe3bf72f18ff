/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 订单异常事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当订单处理过程中出现异常时触发此事件
 * @since 1.0.0
 */
@Getter
public class OrderExceptionEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.ORDER_EXCEPTION;
    private final Long orderId;
    private final String exceptionType;
    private final String exceptionMessage;
    private final String exceptionData;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public OrderExceptionEvent(Long orderId, String exceptionType, String exceptionMessage,
        String exceptionData, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.exceptionType = exceptionType;
        this.exceptionMessage = exceptionMessage;
        this.exceptionData = exceptionData;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("OrderExceptionEvent{orderId=%d, type='%s', message='%s', time=%s}",
            orderId, exceptionType, exceptionMessage, eventTime);
    }
}