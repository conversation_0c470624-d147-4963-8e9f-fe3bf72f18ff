/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.dao.mapper.TenantWarehouseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO;
import com.fulfillmen.shop.domain.entity.TenantWarehouse;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierMultipleOrdersEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.manager.core.repository.TzOrderPurchaseRepository;
import com.fulfillmen.shop.manager.support.alibaba.impl.OrderManager;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/9 15:55
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
public class PurchaseOrderCreatedEventListener {

    private final TzOrderPurchaseRepository purchaseOrderRepository;
    private final OrderManager orderManager;
    private final TzOrderItemMapper orderItemMapper;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TenantWarehouseMapper tenantWarehouseMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public PurchaseOrderCreatedEventListener(TzOrderPurchaseRepository purchaseOrderRepository, OrderManager orderManager, TzOrderItemMapper orderItemMapper,
        TzOrderSupplierMapper orderSupplierMapper,
        TenantWarehouseMapper tenantWarehouseMapper, ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.purchaseOrderRepository = purchaseOrderRepository;
        this.orderManager = orderManager;
        this.orderItemMapper = orderItemMapper;
        this.orderSupplierMapper = orderSupplierMapper;
        this.tenantWarehouseMapper = tenantWarehouseMapper;
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    /**
     * 订单创建事件监听器 - 专门处理 alibaba api 接口处理
     *
     * @param orderCreatedEvent 订单创建事件
     */
    @Async
    @EventListener
    public void onOrderCreatedCallAlibabaApiEvent(OrderCreatedEvent orderCreatedEvent) {
        var purchaseOrderNo = orderCreatedEvent.getPurchaseOrderNo();
        log.info("采购单创建事件监听器 --- 异步调用 alibaba api ---，采购单号 : [{}] ", purchaseOrderNo);
        try {
            createAlibabaOrder(orderCreatedEvent.getOrderContext());
        } catch (Exception e) {
            log.error("创建 1688 订单失败 采购单号：{} ，注意修复处理!! ", purchaseOrderNo, e);
        }
    }

    /**
     * 订单创建事件监听器 - 专门处理 alibaba api 接口处理
     *
     * @param orderSyncAlibabaOrderEvent 同步 1688 订单事件
     */
    @Async
    @EventListener
    public void onOrderCreatedCallAlibabaApiEvent(OrderSyncAlibabaOrderEvent orderSyncAlibabaOrderEvent) {
        var purchaseOrderNo = orderSyncAlibabaOrderEvent.getPurchaseOrderNo();
        log.info("采购单同步 1688 事件监听器 --- 异步调用 alibaba api ---，采购单号 : [{}] ", purchaseOrderNo);
        try {
            // 重新同步的逻辑里，需要判断下。那些订单同步成功
            createAlibabaOrder(orderSyncAlibabaOrderEvent.getOrderContext());
        } catch (Exception e) {
            log.error("创建 1688 订单失败 采购单号：{} ，注意修复处理!! ", purchaseOrderNo, e);
        }
    }

    /**
     * 创建 1688订单
     *
     * @param orderContext 订单上下文
     */
    private void createAlibabaOrder(OrderContext orderContext) {
        List<CompletableFuture<Void>> futures = Lists.newArrayList();
        // 并行创建 1688 订单
        orderContext.getSupplierOrders().forEach(supplierOrder -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 使用现有的供应商订单处理逻辑
                submitSupplierOrderToAlibaba(supplierOrder, orderContext);
            }, threadPoolTaskExecutor);
            futures.add(future);
        });
        // 等待完成
        futures.forEach(CompletableFuture::join);
    }

    /**
     * 提交供应商订单到外部平台
     */
    private void submitSupplierOrderToAlibaba(TzOrderSupplier supplierOrder, OrderContext orderContext) {
        log.info("开始提交供应商订单到外部平台，订单号: {}", supplierOrder.getSupplierOrderNo());
        try {
            // 构建1688订单创建请求
            OrderCreateRequestRecord request = buildOrderCreateRequest(supplierOrder, orderContext);
            // 调用1688订单创建API
            CreateOrderRespDTO createOrderRespDTO = orderManager.createCrossOrder(request);
//        // 更新外部订单信息
            String metadataJson = JacksonUtil.toJsonString(createOrderRespDTO);
            if (createOrderRespDTO.getIsMultipleOrder()) {
                supplierOrder.setIsMultipleOrders(TzOrderSupplierMultipleOrdersEnum.YES);
                // 获取外部订单 id 的列表
                List<String> orderIds = createOrderRespDTO.getOrderList().stream()
                    .map(CreateOrderRespDTO.AlibabaCreateOrderRespDTO::getOrderId)
                    .collect(Collectors.toList());
                supplierOrder.setExternalPlatformOrderIds(JacksonUtil.toJsonString(orderIds));
            } else {
                supplierOrder.setIsMultipleOrders(TzOrderSupplierMultipleOrdersEnum.NO);
                supplierOrder.setExternalPlatformOrderId(createOrderRespDTO.getOrderId());
            }
            supplierOrder.setMetadataJson(metadataJson);
            orderSupplierMapper.updateById(supplierOrder);
            log.info("供应商订单提交成功，外部订单ID: {}", createOrderRespDTO.getOrderId());
        } catch (Exception e) {
            log.warn("供应商订单 {} 处理失败, 请及时修复处理。", supplierOrder.getSupplierOrderNo(), e);
        }
    }

    /**
     * 构建1688订单创建请求
     */
    private OrderCreateRequestRecord buildOrderCreateRequest(TzOrderSupplier supplierOrder, OrderContext orderContext) {
        log.debug("构建1688订单创建请求，供应商订单ID: {}", supplierOrder.getId());
        // 通过租户本地线程上下文，获取默认仓库地址。
        EnhancedTenantContext.TenantWarehouseInfo defaultWarehouse = EnhancedTenantContextHolder
            .getCurrentDefaultWarehouse();
        TenantWarehouse tenantWarehouse = null;
        // 如果默认仓库为空，则从数据库中获取
        if (defaultWarehouse == null) {
            // 1. 获取租户默认仓库地址
            LambdaQueryWrapper<TenantWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            tenantWarehouse = tenantWarehouseMapper.selectOne(
                queryWrapper.eq(TenantWarehouse::getTenantId, UserContextHolder.getTenantId())
                    .eq(TenantWarehouse::getIsDefault, 1));
            if (tenantWarehouse == null) {
                // 请设置租户默认仓库地址，否则无法创建订单
                log.error("请设置租户默认仓库地址，租户ID: {}", UserContextHolder.getTenantId());
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.TENANT_WAREHOUSE_NOT_FOUND);
            }
            defaultWarehouse = EnhancedTenantContext.TenantWarehouseInfo.convertFrom(tenantWarehouse);
        }
        // 2. 使用默认收货地址
        OrderCreateRequestRecord.AddressParamRecord addressParam = OrderCreateRequestRecord.AddressParamRecord.builder()
            // 读取用户姓名
            .fullName(defaultWarehouse.getContactName())
            // 读取用户手机号
            .mobile(defaultWarehouse.getContactMobile())
            // 读取用户邮编
            .postCode(defaultWarehouse.getPostcode())
            // 读取用户城市
            .cityText(defaultWarehouse.getCity())
            // 读取用户省份
            .provinceText(defaultWarehouse.getProvince())
            // 读取用户区县
            .areaText(defaultWarehouse.getDistrict())
            // 读取用户详细地址
            .address(defaultWarehouse.getAddress())
            // 读取用户地区码
            .districtCode(defaultWarehouse.getDistrictCode())
            .build();

        // 3. 构建商品列表
        List<OrderCreateRequestRecord.CargoParamRecord> cargoList = buildCargoList(supplierOrder);
        // 4. 设置采购单号，为下游单号
        String outOrderId = orderContext.getPurchaseOrder().getPurchaseOrderNo();

        return OrderCreateRequestRecord.builder()
            .flow("general")
            .addressParam(addressParam)
            .cargoParamList(cargoList)
            .outOrderId(outOrderId)
            .build();
    }

    /**
     * 根据供应商订单构建商品列表
     */
    private List<OrderCreateRequestRecord.CargoParamRecord> buildCargoList(TzOrderSupplier supplierOrder) {
        // 查询供应商订单对应的商品明细
        List<TzOrderItem> orderItems = orderItemMapper.selectList(
            new LambdaQueryWrapper<TzOrderItem>().eq(TzOrderItem::getSupplierOrderId, supplierOrder.getId()));

        return orderItems.stream()
            .map(orderItem -> {
                //
                String specId = orderItem.getIsSignleItem() == TzProductSpuSingleItemEnum.YES ? null
                    : orderItem.getPlatformSpecId();
                // 如果是单品，则不需要设置specId
                // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                // .openOfferId(orderItem.getOpenOfferId())
                // .outMemberId(orderItem.getOutMemberId())
                return OrderCreateRequestRecord.CargoParamRecord.builder()
                    .offerId(Long.valueOf(orderItem.getPlatformProductId()))
                    // 如果是单品，则不需要设置specId
                    .specId(specId)
                    .quantity(Double.valueOf(orderItem.getQuantity()))
                    // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                    // .openOfferId(orderItem.getOpenOfferId())
                    // .outMemberId(orderItem.getOutMemberId())
                    .build();
            })
            .collect(Collectors.toList());
    }
}
