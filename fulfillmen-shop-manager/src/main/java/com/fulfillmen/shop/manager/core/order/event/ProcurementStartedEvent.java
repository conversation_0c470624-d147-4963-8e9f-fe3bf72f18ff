/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 采购开始事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当开始向供应商采购时触发此事件
 * @since 1.0.0
 */
@Getter
public class ProcurementStartedEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.PROCUREMENT_STARTED;
    private final Long orderId;
    private final String supplierInfo;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public ProcurementStartedEvent(Long orderId, String supplierInfo, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.supplierInfo = supplierInfo;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("ProcurementStartedEvent{orderId=%d, supplier='%s', time=%s}",
            orderId, supplierInfo, eventTime);
    }
}