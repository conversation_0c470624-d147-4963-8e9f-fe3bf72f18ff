/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 采购完成事件
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 当供应商订单创建完成时触发此事件
 * @since 1.0.0
 */
@Getter
public class ProcurementCompletedEvent extends ApplicationEvent {

    private final OrderEventTypeEnums eventType = OrderEventTypeEnums.PROCUREMENT_COMPLETED;
    private final Long orderId;
    private final String externalOrderId;
    private final String supplierOrderData;
    private final String tenantId;
    private final LocalDateTime eventTime;

    public ProcurementCompletedEvent(Long orderId, String externalOrderId,
        String supplierOrderData, String tenantId) {
        super(orderId);
        this.orderId = orderId;
        this.externalOrderId = externalOrderId;
        this.supplierOrderData = supplierOrderData;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("ProcurementCompletedEvent{orderId=%d, externalOrderId='%s', time=%s}",
            orderId, externalOrderId, eventTime);
    }
}