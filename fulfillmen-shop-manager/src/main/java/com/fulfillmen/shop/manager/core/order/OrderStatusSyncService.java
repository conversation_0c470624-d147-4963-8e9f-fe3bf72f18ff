/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.model.OrderStatusCalculationResult;
import com.fulfillmen.shop.domain.service.OrderStatusCalculationService;
import com.fulfillmen.shop.manager.core.order.event.OrderStatusChangeEvent;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 订单状态同步服务 - Manager层
 *
 * <pre>
 * 核心功能：
 * 1. 协调订单状态的同步逻辑
 * 2. 管理状态变更的业务流程
 * 3. 处理跨聚合根的状态同步
 * 4. 发布和处理状态变更事件
 * 5. 集成外部系统和服务
 *
 * 架构定位：
 * - Manager层负责复杂的业务协调
 * - 调用Domain层的状态计算服务
 * - 处理基础设施层的数据访问
 * - 管理事件的发布和订阅
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态同步服务，负责业务流程协调和状态管理
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderStatusSyncService {

    private final OrderStatusCalculationService calculationService;
    private final ApplicationEventPublisher eventPublisher;

    // TODO: 注入实际的Repository或Mapper
    // private final TzOrderPurchaseMapper purchaseOrderMapper;
    // private final TzOrderSupplierMapper supplierOrderMapper;
    // private final TzOrderItemMapper orderItemMapper;

    /**
     * 同步采购订单状态
     *
     * <pre>
     * 业务流程：
     * 1. 查询采购订单和关联的供应商订单
     * 2. 委托Domain层计算新状态
     * 3. 更新数据库中的状态信息
     * 4. 发布状态变更事件
     * 5. 触发后续业务流程
     * </pre>
     *
     * @param purchaseOrderId 采购订单ID
     * @return 同步结果
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderStatusCalculationResult syncPurchaseOrderStatus(Long purchaseOrderId) {
        log.info("开始同步采购订单状态，订单ID: {}", purchaseOrderId);

        try {
            // 1. 获取采购订单信息
            TzOrderPurchase purchaseOrder = getPurchaseOrderById(purchaseOrderId);
            if (purchaseOrder == null) {
                log.warn("采购订单不存在，订单ID: {}", purchaseOrderId);
                throw new IllegalArgumentException("采购订单不存在: " + purchaseOrderId);
            }

            // 2. 获取所有关联的供应商订单状态
            List<TzOrderSupplier> supplierOrders = getSupplierOrdersByPurchaseOrderId(purchaseOrderId);
            if (CollectionUtils.isEmpty(supplierOrders)) {
                log.warn("采购订单没有关联的供应商订单，订单ID: {}", purchaseOrderId);
                return OrderStatusCalculationResult.unchanged(
                    purchaseOrder.getOrderStatus(),
                    List.of()
                );
            }

            List<TzOrderSupplierStatusEnum> supplierStatuses = supplierOrders.stream()
                .map(TzOrderSupplier::getStatus)
                .collect(Collectors.toList());

            // 3. 委托Domain层计算新状态
            OrderStatusCalculationResult result = calculationService.calculatePurchaseOrderStatusWithResult(
                purchaseOrder.getOrderStatus(),
                supplierStatuses
            );

            // 4. 验证计算结果
            if (!calculationService.validateCalculationResult(result)) {
                log.error("状态计算结果验证失败，订单ID: {}", purchaseOrderId);
                throw new IllegalStateException("状态计算结果验证失败");
            }

            // 5. 如果状态发生变化，更新数据库
            if (result.isStatusChanged()) {
                updatePurchaseOrderStatus(purchaseOrder, result);
                log.info("采购订单状态已更新，订单ID: {}, {} -> {}, 原因: {}",
                    purchaseOrderId,
                    result.getOriginalStatus().getDescription(),
                    result.getNewStatus().getDescription(),
                    result.getCalculationReason());
            }

            // 6. 发布状态变更事件
            if (result.isShouldTriggerEvent()) {
                publishStatusChangeEvent(purchaseOrderId, result);
            }

            return result;

        } catch (Exception e) {
            log.error("同步采购订单状态失败，订单ID: {}", purchaseOrderId, e);
            throw new RuntimeException("同步采购订单状态失败", e);
        }
    }

    /**
     * 同步供应商订单状态
     *
     * @param supplierOrderId 供应商订单ID
     * @return 是否发生状态变化
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean syncSupplierOrderStatus(Long supplierOrderId) {
        log.info("开始同步供应商订单状态，订单ID: {}", supplierOrderId);

        try {
            // 1. 获取供应商订单信息
            TzOrderSupplier supplierOrder = getSupplierOrderById(supplierOrderId);
            if (supplierOrder == null) {
                log.warn("供应商订单不存在，订单ID: {}", supplierOrderId);
                return false;
            }

            // 2. 获取所有关联的订单项状态
            List<TzOrderItem> orderItems = getOrderItemsBySupplierOrderId(supplierOrderId);
            if (CollectionUtils.isEmpty(orderItems)) {
                log.warn("供应商订单没有关联的订单项，订单ID: {}", supplierOrderId);
                return false;
            }

            List<TzOrderItemStatusEnum> itemStatuses = orderItems.stream()
                .map(TzOrderItem::getStatus)
                .collect(Collectors.toList());

            // 3. 委托Domain层计算新状态
            TzOrderSupplierStatusEnum currentStatus = supplierOrder.getStatus();
            TzOrderSupplierStatusEnum newStatus = calculationService.getStatusCalculator()
                .calculateSupplierOrderStatus(itemStatuses);

            // 4. 如果状态发生变化，更新数据库
            if (currentStatus != newStatus) {
                updateSupplierOrderStatus(supplierOrder, newStatus);
                log.info("供应商订单状态已更新，订单ID: {}, {} -> {}",
                    supplierOrderId,
                    currentStatus.getDescription(),
                    newStatus.getDescription());

                // 5. 级联同步采购订单状态
                syncPurchaseOrderStatus(supplierOrder.getPurchaseOrderId());

                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("同步供应商订单状态失败，订单ID: {}", supplierOrderId, e);
            throw new RuntimeException("同步供应商订单状态失败", e);
        }
    }

    /**
     * 批量同步多个采购订单的状态
     *
     * @param purchaseOrderIds 采购订单ID列表
     * @return 同步结果列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<OrderStatusCalculationResult> batchSyncPurchaseOrderStatus(List<Long> purchaseOrderIds) {
        log.info("开始批量同步采购订单状态，订单数量: {}", purchaseOrderIds.size());

        return purchaseOrderIds.stream()
            .map(this::syncPurchaseOrderStatus)
            .collect(Collectors.toList());
    }

    /**
     * 当订单项状态发生变化时的级联同步
     *
     * @param orderItemId 订单项ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void cascadeSyncOnOrderItemStatusChange(Long orderItemId) {
        log.info("订单项状态变化，开始级联同步，订单项ID: {}", orderItemId);

        try {
            // 1. 获取订单项信息
            TzOrderItem orderItem = getOrderItemById(orderItemId);
            if (orderItem == null) {
                log.warn("订单项不存在，ID: {}", orderItemId);
                return;
            }

            // 2. 同步供应商订单状态
            boolean supplierStatusChanged = syncSupplierOrderStatus(orderItem.getSupplierOrderId());

            if (supplierStatusChanged) {
                log.info("级联同步完成，订单项ID: {}, 供应商订单状态已更新", orderItemId);
            }

        } catch (Exception e) {
            log.error("级联同步失败，订单项ID: {}", orderItemId, e);
            throw new RuntimeException("级联同步失败", e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 更新采购订单状态
     */
    private void updatePurchaseOrderStatus(TzOrderPurchase purchaseOrder, OrderStatusCalculationResult result) {
        purchaseOrder.setOrderStatus(result.getNewStatus());
        purchaseOrder.setGmtModified(LocalDateTime.now());

        // 根据状态设置特定的时间字段
        switch (result.getNewStatus()) {
            case PAYMENT_COMPLETED -> purchaseOrder.setPaymentDate(LocalDateTime.now());
            case IN_STOCK -> purchaseOrder.setCompletionDate(LocalDateTime.now());
        }

        // TODO: 调用实际的Mapper保存
        // purchaseOrderMapper.updateById(purchaseOrder);
        log.debug("采购订单状态已更新到数据库，订单ID: {}, 新状态: {}",
            purchaseOrder.getId(), result.getNewStatus().getDescription());
    }

    /**
     * 更新供应商订单状态
     */
    private void updateSupplierOrderStatus(TzOrderSupplier supplierOrder, TzOrderSupplierStatusEnum newStatus) {
        supplierOrder.setStatus(newStatus);
        supplierOrder.setGmtModified(LocalDateTime.now());

        // 根据状态设置特定的时间字段
        switch (newStatus) {
            case PAID -> supplierOrder.setPaymentDate(LocalDateTime.now());
            case SHIPPED -> supplierOrder.setShippedDate(LocalDateTime.now());
            case COMPLETED -> supplierOrder.setDeliveredDate(LocalDateTime.now());
        }

        // TODO: 调用实际的Mapper保存
        // supplierOrderMapper.updateById(supplierOrder);
        log.debug("供应商订单状态已更新到数据库，订单ID: {}, 新状态: {}",
            supplierOrder.getId(), newStatus.getDescription());
    }

    /**
     * 发布状态变更事件
     */
    private void publishStatusChangeEvent(Long purchaseOrderId, OrderStatusCalculationResult result) {
        try {
            OrderStatusChangeEvent event = new OrderStatusChangeEvent(
                purchaseOrderId,
                result.getOriginalStatus(),
                result.getNewStatus(),
                result.getCalculationReason(),
                result.getCalculatedAt()
            );

            eventPublisher.publishEvent(event);
            log.debug("状态变更事件已发布，订单ID: {}", purchaseOrderId);

        } catch (Exception e) {
            log.error("发布状态变更事件失败，订单ID: {}", purchaseOrderId, e);
            // 事件发布失败不应该影响主流程
        }
    }

    // ==================== 数据访问方法（待实现） ====================

    private TzOrderPurchase getPurchaseOrderById(Long id) {
        // TODO: 实现实际的数据库查询
        return null;
    }

    private List<TzOrderSupplier> getSupplierOrdersByPurchaseOrderId(Long purchaseOrderId) {
        // TODO: 实现实际的数据库查询
        return List.of();
    }

    private TzOrderSupplier getSupplierOrderById(Long id) {
        // TODO: 实现实际的数据库查询
        return null;
    }

    private List<TzOrderItem> getOrderItemsBySupplierOrderId(Long supplierOrderId) {
        // TODO: 实现实际的数据库查询
        return List.of();
    }

    private TzOrderItem getOrderItemById(Long id) {
        // TODO: 实现实际的数据库查询
        return null;
    }
}
