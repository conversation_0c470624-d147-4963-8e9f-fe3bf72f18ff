/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单状态流转信息
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 描述订单状态的完整流转过程
 * @since 1.0.0
 */
@Getter
@Builder
public class OrderStatusFlow {

    /**
     * 流转ID
     */
    private String flowId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 状态流转步骤列表
     */
    private List<StatusStep> steps;

    /**
     * 流转开始时间
     */
    private LocalDateTime startTime;

    /**
     * 流转结束时间
     */
    private LocalDateTime endTime;

    /**
     * 流转状态
     */
    private FlowStatus status;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 状态流转步骤
     */
    @Getter
    @Builder
    public static class StatusStep {

        /**
         * 步骤序号
         */
        private Integer stepOrder;

        /**
         * 原状态
         */
        private String fromStatus;

        /**
         * 目标状态
         */
        private String toStatus;

        /**
         * 流转原因
         */
        private String reason;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 流转时间
         */
        private LocalDateTime stepTime;

        /**
         * 步骤状态
         */
        private StepStatus stepStatus;

        /**
         * 额外数据
         */
        private String extraData;
    }

    /**
     * 流转状态枚举
     */
    public enum FlowStatus {

        PENDING("待处理"),
        IN_PROGRESS("进行中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        FlowStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 步骤状态枚举
     */
    public enum StepStatus {

        PENDING("待处理"),
        SUCCESS("成功"),
        FAILED("失败"),
        SKIPPED("跳过");

        private final String description;

        StepStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @Override
    public String toString() {
        return String.format("OrderStatusFlow{flowId='%s', orderId=%d, steps=%d, status=%s}",
            flowId, orderId, steps != null ? steps.size() : 0, status);
    }
}