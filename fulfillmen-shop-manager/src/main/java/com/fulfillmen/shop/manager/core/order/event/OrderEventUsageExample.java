/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.manager.core.order.OrderEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 订单事件使用示例
 *
 * <pre>
 * 这个类展示了如何使用统一的订单事件发布器来处理各种订单相关的业务场景。
 *
 * 核心设计思想：
 * 1. 上层业务只需要调用 OrderEventPublisher 的相应方法
 * 2. 所有具体的业务逻辑都在 event 包的监听器中实现
 * 3. 事件发布是同步的，事件处理是异步的
 * 4. 支持事件组合和批量处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description 订单事件使用示例，展示统一事件处理架构的使用方法
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderEventUsageExample {

    private final OrderEventPublisher orderEventPublisher;

    /**
     * 示例1：订单创建完整流程
     *
     * 场景：用户下单后，需要触发采购、通知、统计等一系列操作
     */
    public void exampleOrderCreationFlow(OrderContext orderContext) {
        log.info("=== 订单创建流程示例 ===");

        // 1. 发布订单创建事件
        // 这一个事件会触发所有相关的业务逻辑：
        // - 采购订单创建（PurchaseOrderCreatedEventListener）
        // - 用户通知发送（OrderCreatedEventListener）
        // - 订单统计更新（OrderCreatedEventListener）
        // - 第三方系统集成等
        orderEventPublisher.publishOrderCreatedEvent(orderContext);

        log.info("订单创建事件已发布，相关业务逻辑将异步处理");
    }

    /**
     * 示例2：订单支付流程
     *
     * 场景：用户支付完成后，需要更新订单状态、发送通知、开始采购等
     */
    public void exampleOrderPaymentFlow(Long orderId) {
        log.info("=== 订单支付流程示例 ===");

        // 1. 发布订单支付事件
        orderEventPublisher.publishOrderPaidEvent(
            orderId,
            new BigDecimal("999.99"),
            "ALIPAY",
            "TXN_202507091234567890"
        );

        // 2. 发布采购开始事件
        orderEventPublisher.publishProcurementStartedEvent(
            orderId,
            "阿里巴巴供应商"
        );

        log.info("订单支付相关事件已发布");
    }

    /**
     * 示例3：采购完成流程
     *
     * 场景：供应商订单创建成功后，更新订单信息并通知相关系统
     */
    public void exampleProcurementCompletionFlow(Long orderId, String externalOrderId) {
        log.info("=== 采购完成流程示例 ===");

        // 1. 发布采购完成事件
        orderEventPublisher.publishProcurementCompletedEvent(
            orderId,
            externalOrderId,
            "{\"supplier\":\"alibaba\",\"items\":[{\"id\":123,\"quantity\":5}]}"
        );

        log.info("采购完成事件已发布，订单ID: {}, 外部订单ID: {}", orderId, externalOrderId);
    }

    /**
     * 示例4：物流流程
     *
     * 场景：商品发货、到仓、入库的完整物流流程
     */
    public void exampleLogisticsFlow(Long orderId) {
        log.info("=== 物流流程示例 ===");

        // 1. 商品发货
        orderEventPublisher.publishGoodsShippedEvent(
            orderId,
            "SF202507091234",
            "顺丰快递"
        );

        // 2. 到达仓库
        orderEventPublisher.publishGoodsArrivedWarehouseEvent(
            orderId,
            "WH_SZ_001",
            100
        );

        // 3. 完成入库
        orderEventPublisher.publishGoodsStockedEvent(
            orderId,
            95,
            "质检通过，5件次品已退回"
        );

        log.info("物流流程事件已发布");
    }

    /**
     * 示例5：订单异常处理
     *
     * 场景：订单处理过程中出现异常，需要记录并触发相应的处理流程
     */
    public void exampleOrderExceptionHandling(Long orderId) {
        log.info("=== 订单异常处理示例 ===");

        // 1. 发布订单异常事件
        orderEventPublisher.publishOrderExceptionEvent(
            orderId,
            "PAYMENT_TIMEOUT",
            "支付超时，用户未在30分钟内完成支付",
            "{\"timeout\":1800,\"paymentMethod\":\"ALIPAY\"}"
        );

        // 2. 发布订单取消事件
        orderEventPublisher.publishOrderCancelledEvent(
            orderId,
            "支付超时自动取消",
            "SYSTEM"
        );

        log.info("订单异常处理事件已发布");
    }

    /**
     * 示例6：复杂状态流转
     *
     * 场景：订单状态需要经过多个步骤的复杂流转
     */
    public void exampleComplexStatusFlow(Long orderId) {
        log.info("=== 复杂状态流转示例 ===");

        // 构建状态流转信息
        OrderStatusFlow statusFlow = OrderStatusFlow.builder()
            .flowId("FLOW_" + orderId + "_" + System.currentTimeMillis())
            .orderId(orderId)
            .steps(Arrays.asList(
                OrderStatusFlow.StatusStep.builder()
                    .stepOrder(1)
                    .fromStatus("PENDING_PAYMENT")
                    .toStatus("PAID")
                    .reason("用户支付完成")
                    .operator("USER")
                    .stepTime(java.time.LocalDateTime.now())
                    .stepStatus(OrderStatusFlow.StepStatus.SUCCESS)
                    .build(),
                OrderStatusFlow.StatusStep.builder()
                    .stepOrder(2)
                    .fromStatus("PAID")
                    .toStatus("PROCUREMENT_IN_PROGRESS")
                    .reason("开始采购")
                    .operator("SYSTEM")
                    .stepTime(java.time.LocalDateTime.now().plusMinutes(1))
                    .stepStatus(OrderStatusFlow.StepStatus.SUCCESS)
                    .build()
            ))
            .startTime(java.time.LocalDateTime.now())
            .status(OrderStatusFlow.FlowStatus.IN_PROGRESS)
            .notes("订单正常流转")
            .build();

        // 发布状态流转事件
        orderEventPublisher.publishOrderStatusFlowEvent(orderId, statusFlow);

        log.info("复杂状态流转事件已发布");
    }

    /**
     * 示例7：事件组合发布
     *
     * 场景：需要同时触发多个相关事件，保证原子性和一致性
     */
    public void exampleEventGroupPublication(Long orderId) {
        log.info("=== 事件组合发布示例 ===");

        // 创建事件组合
        OrderEventGroup eventGroup = OrderEventGroup.createOrderLifecycleGroup(
            orderId,
            Arrays.asList(
                new OrderPaidEvent(orderId, new BigDecimal("999.99"), "ALIPAY", "TXN_123", "1001", "2001"),
                new ProcurementStartedEvent(orderId, "阿里巴巴供应商", "1001"),
                new GoodsShippedEvent(orderId, "SF202507091234", "顺丰快递", "1001")
            ),
            "1001"
        );

        // 发布事件组合
        orderEventPublisher.publishOrderEventGroup(eventGroup);

        log.info("事件组合已发布，组ID: {}", eventGroup.getGroupId());
    }

    /**
     * 示例8：订单完成流程
     *
     * 场景：订单履约完成，触发最终的完成处理
     */
    public void exampleOrderCompletionFlow(Long orderId) {
        log.info("=== 订单完成流程示例 ===");

        // 发布订单完成事件
        orderEventPublisher.publishOrderCompletedEvent(
            orderId,
            "订单已成功履约，客户满意度调查已发送"
        );

        log.info("订单完成事件已发布");
    }
}
