/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.callback;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 1688 回调通知
 *
 * <AUTHOR>
 * @date 2025/7/9 18:54
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/alibaba/callback")
public class WebhookApi {

    @PostMapping()
    public String callBack(@RequestParam("_aop_signature") String signature, String message) {
        log.info("message : [{}] , signature : [{}]", message, signature);
        return "success";
    }
}
