/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 1688 回调通知
 *
 * <AUTHOR>
 * @date 2025/7/9 18:54
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/alibaba/callback")
public class WebhookApi {

    @PostMapping()
    public String callBack(@RequestParam("_aop_signature") String signature, @RequestBody String message) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("收到阿里巴巴webhook回调，消息长度: {}", message.length());

            // 处理消息
//            List<MessageResult> results = messageDispatcher.dispatch(requestBody, signature);

            long duration = System.currentTimeMillis() - startTime;
//            log.info("Webhook消息处理完成，处理时间: {}ms，处理结果数量: {}", duration, results.size());
        } catch (WebhookProcessingException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Webhook消息处理失败，处理时间: {} ms", duration, e);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Webhook消息处理异常，处理时间: {} ms", duration, e);
        }
        return "success";
    }
}
