/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.util;

import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 货币转换工具类测试
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @since 1.0.0
 */
class CurrencyConversionUtilsTest {

    private static final Logger log = LoggerFactory.getLogger(CurrencyConversionUtilsTest.class);

    @BeforeEach
    void setUp() {
        // 清空缓存，确保测试使用默认汇率
        CurrencyConversionUtils.clearCache();

        // 设置测试汇率数据
        CurrencyConversionUtils.updateExchangeRate("CNY", "USD", new BigDecimal("0.13"));
        CurrencyConversionUtils.updateExchangeRate("CNY", "EUR", new BigDecimal("0.11"));
    }

    @Test
    @DisplayName("测试最小价格保护机制 - CNY转USD极小金额")
    void testMinimumPriceProtection_CnyToUsd_VerySmallAmount() {
        // 测试极小金额：0.03 CNY
        BigDecimal smallAmount = new BigDecimal("0.03");
        BigDecimal result = CurrencyConversionUtils.convertCurrency(smallAmount, "CNY", "USD");

        assertNotNull(result, "转换结果不应为null");
        assertEquals(new BigDecimal("0.01"), result, "极小金额应被保护为最小价格0.01");

        log.info("极小金额转换测试: {} CNY -> {} USD", smallAmount, result);
    }

    @Test
    @DisplayName("测试最小价格保护机制 - CNY转EUR极小金额")
    void testMinimumPriceProtection_CnyToEur_VerySmallAmount() {
        // 测试极小金额：0.05 CNY
        BigDecimal smallAmount = new BigDecimal("0.05");
        BigDecimal result = CurrencyConversionUtils.convertCurrency(smallAmount, "CNY", "EUR");

        assertNotNull(result, "转换结果不应为null");
        assertEquals(new BigDecimal("0.01"), result, "极小金额应被保护为最小价格0.01");

        log.info("极小金额转换测试: {} CNY -> {} EUR", smallAmount, result);
    }

    @Test
    @DisplayName("测试最小价格保护机制 - 零金额")
    void testMinimumPriceProtection_ZeroAmount() {
        BigDecimal zeroAmount = BigDecimal.ZERO;
        BigDecimal result = CurrencyConversionUtils.convertCurrency(zeroAmount, "CNY", "USD");

        assertNotNull(result, "转换结果不应为null");
        assertEquals(new BigDecimal("0.01"), result, "零金额应被保护为最小价格0.01");

        log.info("零金额转换测试: {} CNY -> {} USD", zeroAmount, result);
    }

    @Test
    @DisplayName("测试正常金额转换 - 不触发最小价格保护")
    void testNormalAmountConversion_NoProtection() {
        // 测试正常金额：1.00 CNY
        BigDecimal normalAmount = new BigDecimal("1.00");
        BigDecimal result = CurrencyConversionUtils.convertCurrency(normalAmount, "CNY", "USD");

        assertNotNull(result, "转换结果不应为null");

        // 计算预期结果：1.00 * 0.13 = 0.13，截断后为0.13
        BigDecimal expected = new BigDecimal("0.13");
        assertEquals(expected, result, "正常金额转换应按汇率计算");

        log.info("正常金额转换测试: {} CNY -> {} USD", normalAmount, result);
    }

    @Test
    @DisplayName("测试截断逻辑 - 保留两位小数直接截断")
    void testTruncationLogic() {
        // 设置一个会产生多位小数的汇率
        CurrencyConversionUtils.updateExchangeRate("CNY", "USD", new BigDecimal("0.137856"));

        BigDecimal amount = new BigDecimal("1.00");
        BigDecimal result = CurrencyConversionUtils.convertCurrency(amount, "CNY", "USD");

        assertNotNull(result, "转换结果不应为null");

        // 1.00 * 0.137856 = 0.137856，截断后应为0.13（不是四舍五入的0.14）
        BigDecimal expected = new BigDecimal("0.13");
        assertEquals(expected, result, "应该使用截断逻辑，而不是四舍五入");

        log.info("截断逻辑测试: {} CNY -> {} USD (汇率: 0.137856)", amount, result);
    }

    @Test
    @DisplayName("测试边界值 - 刚好0.01的转换结果")
    void testBoundaryValue_ExactlyMinimum() {
        // 设置汇率使得转换结果刚好为0.01
        CurrencyConversionUtils.updateExchangeRate("CNY", "USD", new BigDecimal("0.10"));

        BigDecimal amount = new BigDecimal("0.10");
        BigDecimal result = CurrencyConversionUtils.convertCurrency(amount, "CNY", "USD");

        assertNotNull(result, "转换结果不应为null");
        assertEquals(new BigDecimal("0.01"), result, "刚好0.01的结果应该保持不变");

        log.info("边界值测试: {} CNY -> {} USD", amount, result);
    }

    @Test
    @DisplayName("测试相同货币转换")
    void testSameCurrencyConversion() {
        BigDecimal amount = new BigDecimal("0.005");
        BigDecimal result = CurrencyConversionUtils.convertCurrency(amount, "CNY", "CNY");

        assertNotNull(result, "转换结果不应为null");
        assertEquals(amount, result, "相同货币转换应返回原金额，不应用最小价格保护");

        log.info("相同货币转换测试: {} CNY -> {} CNY", amount, result);
    }

    @Test
    @DisplayName("测试多种货币转换场景")
    void testMultipleCurrencyScenarios() {
        // 测试各种货币对的极小金额转换
        String[] currencies = {"USD", "EUR", "JPY", "KRW", "INR"};
        BigDecimal smallAmount = new BigDecimal("0.01");

        for (String targetCurrency : currencies) {
            if (!"CNY".equals(targetCurrency)) {
                BigDecimal result = CurrencyConversionUtils.convertCurrency(smallAmount, "CNY", targetCurrency);

                assertNotNull(result, "转换结果不应为null: CNY -> " + targetCurrency);
                assertTrue(result.compareTo(BigDecimal.ZERO) > 0,
                    "转换结果应大于0: CNY -> " + targetCurrency);

                log.info("多货币转换测试: {} CNY -> {} {}", smallAmount, result, targetCurrency);
            }
        }
    }

    @Test
    @DisplayName("测试null和无效参数处理")
    void testNullAndInvalidParameters() {
        // 测试null参数
        assertNull(CurrencyConversionUtils.convertCurrency(null, "CNY", "USD"));
        assertNull(CurrencyConversionUtils.convertCurrency(new BigDecimal("1.00"), null, "USD"));
        assertNull(CurrencyConversionUtils.convertCurrency(new BigDecimal("1.00"), "CNY", null));

        // 测试无效货币代码
        assertNull(CurrencyConversionUtils.convertCurrency(new BigDecimal("1.00"), "INVALID", "USD"));
        assertNull(CurrencyConversionUtils.convertCurrency(new BigDecimal("1.00"), "CNY", "INVALID"));

        log.info("无效参数处理测试通过");
    }
}
