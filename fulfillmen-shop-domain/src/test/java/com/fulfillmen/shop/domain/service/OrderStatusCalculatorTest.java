/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 订单状态计算器测试
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 测试订单状态计算器的各种场景
 * @since 1.0.0
 */
class OrderStatusCalculatorTest {

    private OrderStatusCalculator calculator;

    @BeforeEach
    void setUp() {
        calculator = new OrderStatusCalculator();
    }

    @Test
    @DisplayName("测试空列表的采购订单状态计算")
    void testCalculatePurchaseOrderStatus_EmptyList() {
        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(Collections.emptyList());
        assertEquals(TzOrderPurchaseStatusEnum.PAYMENT_PENDING, result);
    }

    @Test
    @DisplayName("测试所有供应商订单都已取消的情况")
    void testCalculatePurchaseOrderStatus_AllCancelled() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.CANCELLED,
            TzOrderSupplierStatusEnum.CANCELLED
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.ORDER_CANCELLED, result);
    }

    @Test
    @DisplayName("测试存在待支付供应商订单的情况")
    void testCalculatePurchaseOrderStatus_HasPendingPayment() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.PENDING_PAYMENT,
            TzOrderSupplierStatusEnum.PAID
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.PAYMENT_PENDING, result);
    }

    @Test
    @DisplayName("测试所有供应商订单都已完成的情况")
    void testCalculatePurchaseOrderStatus_AllCompleted() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.COMPLETED
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.IN_STOCK, result);
    }

    @Test
    @DisplayName("测试所有供应商订单都已送达仓库的情况")
    void testCalculatePurchaseOrderStatus_AllDeliveredToWarehouse() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE,
            TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE,
            TzOrderSupplierStatusEnum.COMPLETED
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED, result);
    }

    @Test
    @DisplayName("测试部分供应商订单已完成的情况")
    void testCalculatePurchaseOrderStatus_PartiallyCompleted() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED, result);
    }

    @Test
    @DisplayName("测试所有供应商订单都已发货的情况")
    void testCalculatePurchaseOrderStatus_AllShipped() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.SHIPPED
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED, result);
    }

    @Test
    @DisplayName("测试所有供应商订单都在采购中的情况")
    void testCalculatePurchaseOrderStatus_AllInProcurement() {
        List<TzOrderSupplierStatusEnum> statuses = Arrays.asList(
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderSupplierStatusEnum.PAID,
            TzOrderSupplierStatusEnum.PENDING_SHIPMENT
        );

        TzOrderPurchaseStatusEnum result = calculator.calculatePurchaseOrderStatus(statuses);
        assertEquals(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS, result);
    }

    @Test
    @DisplayName("测试空列表的供应商订单状态计算")
    void testCalculateSupplierOrderStatus_EmptyList() {
        TzOrderSupplierStatusEnum result = calculator.calculateSupplierOrderStatus(Collections.emptyList());
        assertEquals(TzOrderSupplierStatusEnum.PENDING_PAYMENT, result);
    }

    @Test
    @DisplayName("测试所有订单项都已完成的情况")
    void testCalculateSupplierOrderStatus_AllItemsCompleted() {
        List<TzOrderItemStatusEnum> statuses = Arrays.asList(
            TzOrderItemStatusEnum.COMPLETED,
            TzOrderItemStatusEnum.COMPLETED
        );

        TzOrderSupplierStatusEnum result = calculator.calculateSupplierOrderStatus(statuses);
        assertEquals(TzOrderSupplierStatusEnum.COMPLETED, result);
    }

    @Test
    @DisplayName("测试所有订单项都已送达的情况")
    void testCalculateSupplierOrderStatus_AllItemsDelivered() {
        List<TzOrderItemStatusEnum> statuses = Arrays.asList(
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.DELIVERED,
            TzOrderItemStatusEnum.COMPLETED
        );

        TzOrderSupplierStatusEnum result = calculator.calculateSupplierOrderStatus(statuses);
        assertEquals(TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE, result);
    }

    @Test
    @DisplayName("测试部分订单项已发货的情况")
    void testCalculateSupplierOrderStatus_PartiallyShipped() {
        List<TzOrderItemStatusEnum> statuses = Arrays.asList(
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS
        );

        TzOrderSupplierStatusEnum result = calculator.calculateSupplierOrderStatus(statuses);
        assertEquals(TzOrderSupplierStatusEnum.PARTIALLY_SHIPPED, result);
    }

    @Test
    @DisplayName("测试所有订单项都已发货的情况")
    void testCalculateSupplierOrderStatus_AllItemsShipped() {
        List<TzOrderItemStatusEnum> statuses = Arrays.asList(
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.SHIPPED,
            TzOrderItemStatusEnum.DELIVERED
        );

        TzOrderSupplierStatusEnum result = calculator.calculateSupplierOrderStatus(statuses);
        assertEquals(TzOrderSupplierStatusEnum.SHIPPED, result);
    }

    @Test
    @DisplayName("测试状态流转验证 - 合法流转")
    void testIsValidStatusTransition_ValidTransition() {
        assertTrue(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.PAYMENT_PENDING,
            TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED));

        assertTrue(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED));
    }

    @Test
    @DisplayName("测试状态流转验证 - 非法流转")
    void testIsValidStatusTransition_InvalidTransition() {
        // 已完成状态不能再变更
        assertFalse(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.IN_STOCK,
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED));

        // 已取消状态不能再变更
        assertFalse(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.ORDER_CANCELLED,
            TzOrderPurchaseStatusEnum.PAYMENT_PENDING));

        // 不能倒退到更早状态（除了取消）
        assertFalse(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED));
    }

    @Test
    @DisplayName("测试状态流转验证 - 取消状态的特殊情况")
    void testIsValidStatusTransition_CancellationSpecialCase() {
        // 任何状态都可以变更为取消状态
        assertTrue(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.PAYMENT_PENDING,
            TzOrderPurchaseStatusEnum.ORDER_CANCELLED));

        assertTrue(calculator.isValidStatusTransition(
            TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED,
            TzOrderPurchaseStatusEnum.ORDER_CANCELLED));
    }
}
