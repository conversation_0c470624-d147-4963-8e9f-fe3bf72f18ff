/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.domain.model.OrderStatusCalculationResult;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 订单状态管理性能测试
 *
 * <pre>
 * 测试状态管理系统的性能指标：
 * 1. 单次状态计算性能
 * 2. 批量状态计算性能
 * 3. 并发状态计算性能
 * 4. 内存使用情况
 * 5. 系统稳定性验证
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态管理性能测试
 * @since 1.0.0
 */
class OrderStatusPerformanceTest {

    private OrderStatusCalculator statusCalculator;
    private OrderStatusCalculationService calculationService;

    @BeforeEach
    void setUp() {
        statusCalculator = new OrderStatusCalculator();
        calculationService = new OrderStatusCalculationService(statusCalculator);
    }

    @Test
    @DisplayName("测试单次状态计算性能")
    void testSingleCalculationPerformance() {
        // 准备测试数据
        List<TzOrderSupplierStatusEnum> supplierStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderSupplierStatusEnum.PAID,
            TzOrderSupplierStatusEnum.DELIVERED_TO_WAREHOUSE
        );

        // 预热
        for (int i = 0; i < 100; i++) {
            statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
        }

        // 性能测试
        long startTime = System.nanoTime();
        int iterations = 10000;

        for (int i = 0; i < iterations; i++) {
            TzOrderPurchaseStatusEnum result = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
            assertNotNull(result);
        }

        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        double avgTimePerCalculation = (double) duration / iterations / 1_000_000; // 转换为毫秒

        System.out.printf("单次状态计算性能测试结果:\n");
        System.out.printf("- 总计算次数: %d\n", iterations);
        System.out.printf("- 总耗时: %.2f ms\n", duration / 1_000_000.0);
        System.out.printf("- 平均每次计算耗时: %.4f ms\n", avgTimePerCalculation);
        System.out.printf("- 每秒可处理计算次数: %.0f\n", 1000.0 / avgTimePerCalculation);

        // 性能断言：平均每次计算应该在1毫秒以内
        assertTrue(avgTimePerCalculation < 1.0,
            String.format("单次计算耗时过长: %.4f ms", avgTimePerCalculation));
    }

    @Test
    @DisplayName("测试批量状态计算性能")
    void testBatchCalculationPerformance() {
        // 准备大量测试数据
        List<OrderStatusCalculationService.PurchaseOrderStatusData> orderDataList = new ArrayList<>();

        for (int i = 0; i < 1000; i++) {
            List<TzOrderSupplierStatusEnum> statuses = generateRandomSupplierStatuses(5);
            orderDataList.add(new OrderStatusCalculationService.PurchaseOrderStatusData(
                (long) i,
                TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS,
                statuses
            ));
        }

        // 预热
        calculationService.batchCalculatePurchaseOrderStatus(orderDataList.subList(0, 100));

        // 性能测试
        long startTime = System.nanoTime();
        List<OrderStatusCalculationResult> results = calculationService.batchCalculatePurchaseOrderStatus(orderDataList);
        long endTime = System.nanoTime();

        long duration = endTime - startTime;
        double avgTimePerOrder = (double) duration / orderDataList.size() / 1_000_000; // 转换为毫秒

        System.out.printf("批量状态计算性能测试结果:\n");
        System.out.printf("- 订单数量: %d\n", orderDataList.size());
        System.out.printf("- 总耗时: %.2f ms\n", duration / 1_000_000.0);
        System.out.printf("- 平均每个订单计算耗时: %.4f ms\n", avgTimePerOrder);
        System.out.printf("- 每秒可处理订单数: %.0f\n", 1000.0 / avgTimePerOrder);

        // 验证结果
        assertEquals(orderDataList.size(), results.size());
        assertNotNull(results.get(0));

        // 性能断言：平均每个订单计算应该在2毫秒以内
        assertTrue(avgTimePerOrder < 2.0,
            String.format("批量计算耗时过长: %.4f ms per order", avgTimePerOrder));
    }

    @Test
    @DisplayName("测试并发状态计算性能")
    void testConcurrentCalculationPerformance() throws InterruptedException {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        int totalTasks = 1000;
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 准备测试数据
        List<TzOrderSupplierStatusEnum> supplierStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS
        );

        long startTime = System.nanoTime();

        // 创建并发任务
        for (int i = 0; i < totalTasks; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 每个任务执行多次计算
                for (int j = 0; j < 10; j++) {
                    TzOrderPurchaseStatusEnum result = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
                    assertNotNull(result);
                }
            }, executor);
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        long endTime = System.nanoTime();
        long duration = endTime - startTime;

        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));

        int totalCalculations = totalTasks * 10;
        double avgTimePerCalculation = (double) duration / totalCalculations / 1_000_000; // 转换为毫秒

        System.out.printf("并发状态计算性能测试结果:\n");
        System.out.printf("- 并发线程数: 10\n");
        System.out.printf("- 总任务数: %d\n", totalTasks);
        System.out.printf("- 总计算次数: %d\n", totalCalculations);
        System.out.printf("- 总耗时: %.2f ms\n", duration / 1_000_000.0);
        System.out.printf("- 平均每次计算耗时: %.4f ms\n", avgTimePerCalculation);
        System.out.printf("- 并发吞吐量: %.0f calculations/second\n", totalCalculations * 1000.0 / (duration / 1_000_000.0));

        // 性能断言：并发情况下平均每次计算应该在5毫秒以内
        assertTrue(avgTimePerCalculation < 5.0,
            String.format("并发计算耗时过长: %.4f ms", avgTimePerCalculation));
    }

    @Test
    @DisplayName("测试内存使用情况")
    void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();

        // 强制垃圾回收
        System.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        // 执行大量计算
        List<TzOrderSupplierStatusEnum> supplierStatuses = generateRandomSupplierStatuses(10);

        for (int i = 0; i < 10000; i++) {
            TzOrderPurchaseStatusEnum result = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
            assertNotNull(result);

            // 偶尔创建新的状态列表以测试内存分配
            if (i % 1000 == 0) {
                supplierStatuses = generateRandomSupplierStatuses(10);
            }
        }

        // 再次强制垃圾回收
        System.gc();
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();

        long memoryUsed = finalMemory - initialMemory;

        System.out.printf("内存使用测试结果:\n");
        System.out.printf("- 初始内存使用: %.2f MB\n", initialMemory / 1024.0 / 1024.0);
        System.out.printf("- 最终内存使用: %.2f MB\n", finalMemory / 1024.0 / 1024.0);
        System.out.printf("- 内存增长: %.2f MB\n", memoryUsed / 1024.0 / 1024.0);

        // 内存断言：内存增长应该在合理范围内（小于10MB）
        assertTrue(memoryUsed < 10 * 1024 * 1024,
            String.format("内存使用过多: %.2f MB", memoryUsed / 1024.0 / 1024.0));
    }

    @Test
    @DisplayName("测试系统稳定性")
    void testSystemStability() {
        // 长时间运行测试，验证系统稳定性
        int totalIterations = 50000;
        int errorCount = 0;

        List<TzOrderSupplierStatusEnum> supplierStatuses = Arrays.asList(
            TzOrderSupplierStatusEnum.COMPLETED,
            TzOrderSupplierStatusEnum.SHIPPED,
            TzOrderSupplierStatusEnum.PROCUREMENT_IN_PROGRESS,
            TzOrderSupplierStatusEnum.PAID
        );

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < totalIterations; i++) {
            try {
                TzOrderPurchaseStatusEnum result = statusCalculator.calculatePurchaseOrderStatus(supplierStatuses);
                assertNotNull(result);

                // 验证结果的一致性
                assertEquals(TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED, result);

            } catch (Exception e) {
                errorCount++;
                System.err.printf("第 %d 次计算出现错误: %s\n", i, e.getMessage());
            }

            // 每10000次输出一次进度
            if (i % 10000 == 0 && i > 0) {
                System.out.printf("已完成 %d/%d 次计算\n", i, totalIterations);
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.printf("系统稳定性测试结果:\n");
        System.out.printf("- 总计算次数: %d\n", totalIterations);
        System.out.printf("- 错误次数: %d\n", errorCount);
        System.out.printf("- 成功率: %.4f%%\n", (totalIterations - errorCount) * 100.0 / totalIterations);
        System.out.printf("- 总耗时: %d ms\n", duration);
        System.out.printf("- 平均TPS: %.2f\n", totalIterations * 1000.0 / duration);

        // 稳定性断言：错误率应该为0
        assertEquals(0, errorCount, "系统应该保持100%稳定性");
    }

    /**
     * 生成随机的供应商订单状态列表
     */
    private List<TzOrderSupplierStatusEnum> generateRandomSupplierStatuses(int count) {
        TzOrderSupplierStatusEnum[] allStatuses = TzOrderSupplierStatusEnum.values();
        List<TzOrderSupplierStatusEnum> statuses = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            // 排除CANCELLED状态以避免影响测试结果
            TzOrderSupplierStatusEnum status;
            do {
                status = allStatuses[(int) (Math.random() * allStatuses.length)];
            } while (status == TzOrderSupplierStatusEnum.CANCELLED);

            statuses.add(status);
        }

        return statuses;
    }
}
