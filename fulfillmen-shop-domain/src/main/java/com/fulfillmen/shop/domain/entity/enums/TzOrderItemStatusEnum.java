/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 订单项状态枚举
 *
 * <pre>
 * 订单项状态流转：
 *
 * 0. 待处理 (Pending)
 * - 订单项刚创建，等待开始采购
 *
 * 1. 采购中 (Procurement In Progress)
 * - 正在向供应商下单采购此商品
 *
 * 2. 已发货 (Shipped)
 * - 供应商已发货，商品在运输途中
 *
 * 3. 已送达 (Delivered)
 * - 商品已送达WMS仓库
 *
 * 4. 已完成 (Completed)
 * - 商品已完成质检并入库
 *
 * 5. 失败 (Failed)
 * - 采购失败或其他异常情况
 *
 * 6. 已取消 (Cancelled)
 * - 订单项被取消
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/25 15:13
 * @description 订单项状态枚举，用于细粒度跟踪每个商品的履约状态
 * @since 1.0.0
 */
@Getter
public enum TzOrderItemStatusEnum {

    /**
     * 待处理
     * <pre>
     * 订单项刚创建，等待开始采购流程
     * </pre>
     */
    PENDING(0, "待处理"),

    /**
     * 采购中
     * <pre>
     * 正在向供应商下单采购此商品
     * </pre>
     */
    PROCUREMENT_IN_PROGRESS(1, "采购中"),

    /**
     * 已发货
     * <pre>
     * 供应商已发货，商品在运输途中
     * </pre>
     */
    SHIPPED(2, "已发货"),

    /**
     * 已送达
     * <pre>
     * 商品已送达WMS仓库，等待质检
     * </pre>
     */
    DELIVERED(3, "已送达"),

    /**
     * 已完成
     * <pre>
     * 商品已完成质检并入库，可以发给客户
     * </pre>
     */
    COMPLETED(4, "已完成"),

    /**
     * 失败
     * <pre>
     * 采购失败、质检不合格或其他异常情况
     * </pre>
     */
    FAILED(5, "失败"),

    /**
     * 已取消
     * <pre>
     * 订单项被取消，不再进行后续处理
     * </pre>
     */
    CANCELLED(6, "已取消");

    @EnumValue
    private final int code;
    private final String desc;

    TzOrderItemStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static TzOrderItemStatusEnum getByCode(int code) {
        for (TzOrderItemStatusEnum status : TzOrderItemStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED || this == CANCELLED;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == COMPLETED;
    }
}
