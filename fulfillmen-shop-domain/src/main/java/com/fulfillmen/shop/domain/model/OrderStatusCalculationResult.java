/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.model;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单状态计算结果
 *
 * <pre>
 * 封装状态计算的结果信息，包括：
 * 1. 计算得出的新状态
 * 2. 状态是否发生变化
 * 3. 计算依据和统计信息
 * 4. 计算时间戳
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 订单状态计算结果封装类
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderStatusCalculationResult {

    /**
     * 计算得出的新状态
     */
    private TzOrderPurchaseStatusEnum newStatus;

    /**
     * 原始状态
     */
    private TzOrderPurchaseStatusEnum originalStatus;

    /**
     * 状态是否发生变化
     */
    private boolean statusChanged;

    /**
     * 计算依据的供应商订单状态列表
     */
    private List<TzOrderSupplierStatusEnum> supplierStatuses;

    /**
     * 供应商订单总数
     */
    private int totalSupplierOrders;

    /**
     * 已完成的供应商订单数
     */
    private int completedSupplierOrders;

    /**
     * 计算时间
     */
    private LocalDateTime calculatedAt;

    /**
     * 计算说明
     */
    private String calculationReason;

    /**
     * 是否需要触发事件
     */
    private boolean shouldTriggerEvent;

    /**
     * 创建状态变化的结果
     */
    public static OrderStatusCalculationResult changed(TzOrderPurchaseStatusEnum originalStatus,
        TzOrderPurchaseStatusEnum newStatus,
        List<TzOrderSupplierStatusEnum> supplierStatuses,
        String reason) {
        return OrderStatusCalculationResult.builder()
            .originalStatus(originalStatus)
            .newStatus(newStatus)
            .statusChanged(true)
            .supplierStatuses(supplierStatuses)
            .totalSupplierOrders(supplierStatuses.size())
            .completedSupplierOrders((int) supplierStatuses.stream()
                .filter(status -> status == TzOrderSupplierStatusEnum.COMPLETED)
                .count())
            .calculatedAt(LocalDateTime.now())
            .calculationReason(reason)
            .shouldTriggerEvent(true)
            .build();
    }

    /**
     * 创建状态未变化的结果
     */
    public static OrderStatusCalculationResult unchanged(TzOrderPurchaseStatusEnum currentStatus,
        List<TzOrderSupplierStatusEnum> supplierStatuses) {
        return OrderStatusCalculationResult.builder()
            .originalStatus(currentStatus)
            .newStatus(currentStatus)
            .statusChanged(false)
            .supplierStatuses(supplierStatuses)
            .totalSupplierOrders(supplierStatuses.size())
            .completedSupplierOrders((int) supplierStatuses.stream()
                .filter(status -> status == TzOrderSupplierStatusEnum.COMPLETED)
                .count())
            .calculatedAt(LocalDateTime.now())
            .calculationReason("状态无变化")
            .shouldTriggerEvent(false)
            .build();
    }

    /**
     * 获取完成进度百分比
     */
    public double getCompletionPercentage() {
        if (totalSupplierOrders == 0) {
            return 0.0;
        }
        return (double) completedSupplierOrders / totalSupplierOrders * 100;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return newStatus == TzOrderPurchaseStatusEnum.IN_STOCK ||
            newStatus == TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
    }
}
