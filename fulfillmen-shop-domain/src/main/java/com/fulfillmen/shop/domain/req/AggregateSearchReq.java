/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.req;

import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fulfillmen.shop.domain.enums.CurrencyCodeEnum;
import com.fulfillmen.shop.domain.validation.ValidAggregateSearch;
import com.fulfillmen.starter.core.validation.constraints.EnumValue;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord.SaleFilterParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

/**
 * 聚合搜索请求对象
 *
 * <AUTHOR>
 * @date 2025-03-10
 * @description: 聚合搜索请求参数，支持关键词搜索和图片搜索
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ValidAggregateSearch
public class AggregateSearchReq {

    /**
     * 搜索类型 1: 关键词搜索 2: 图片搜索
     */
    @Schema(description = "搜索类型 1: 关键词搜索 (默认) 2: 图片搜索 (需提供imageId 或 imageUrl , 可选提供keyword)", defaultValue = "1")
    private int searchType = SearchType.KEYWORD.getValue();

    /**
     * 关键词
     */
    @Schema(description = "关键词")
    private String keyword;

    /**
     * 图片ID
     */
    @Schema(description = "图片ID")
    private String imageId;

    /**
     * 页码
     */
    @Min(value = 1, message = "{validation.page.min}")
    @Schema(description = "页码", defaultValue = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Max(value = 50, message = "{validation.page.size.max}")
    @Schema(description = "每页大小", defaultValue = "20")
    private Integer pageSize = 20;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private Long categoryId;

    /**
     * 分类ID列表
     */
    @Schema(description = "分类ID列表")
    private List<Long> categoryIdList;

    /**
     * 最小价格
     */
    @Schema(description = "最小价格")
    private String minPrice;

    /**
     * 最大价格
     */
    @Schema(description = "最大价格")
    private String maxPrice;

    /**
     * 货币代码 , 根据前端请求当前的货币类型进行转换成 CNY 价格查询 1688 接口
     *
     * <pre>
     * 价格的货币类型，默认 CNY
     * 支持 CNY, USD, EUR, JPY, KRW, INR
     * 提供前端检索价格的时候，需要根据货币代码 转换成人民币价格 提供给 1688 转换对应的价格
     * </pre>
     */
    @Schema(description = "货币代码", defaultValue = "CNY")
    private CurrencyCodeEnum currencyCode = CurrencyCodeEnum.CNY;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private String sortField;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向")
    private String sortOrder;

    /**
     * 过滤条件
     */
    @Schema(description = "过滤条件")
    private String filter;

    /**
     * 商品集合ID
     */
    @Schema(description = "商品集合ID")
    private String snId;

    /**
     * 关键词翻译
     */
    @Schema(description = "关键词翻译")
    private Boolean keywordTranslate;

    /**
     * 图片URL
     *
     * <pre>
     * 1. 图片URL 和 imageId 二选一
     * 2. 如果同时提供，则优先使用图片URL
     * 3. 如果都不提供，则抛出异常
     * </pre>
     */
    @Schema(description = "图片URL")
    private String imageUrl;

    /**
     * 销量筛选参数
     */
    @Schema(description = "销量筛选参数")
    private List<SaleFilterParam> saleFilterParams;

    /**
     * 构建聚合搜索缓存Key
     *
     * <pre>
     * Key格式: aggregate:searchType:keyword:imageId:page:pageSize:其他参数
     * 示例:
     * - aggregate:1:iphone:null:1:20:... (关键词搜索)
     * - aggregate:2:null:img123:1:20:... (图片搜索)
     * - aggregate:1:phone:img123:1:20:... (混合搜索)
     * </pre>
     */
    public String buildAggregateSearchCacheKey() {
        StringBuilder keyBuilder = new StringBuilder("aggregate:");

        // 搜索类型 (必需)
        keyBuilder.append(this.getSearchType()).append(":");

        // 关键词 (可选) - 使用MD5哈希避免特殊字符问题
        appendHashedValue(keyBuilder, this.getKeyword());

        // 图片ID (可选) - 使用MD5哈希
        appendHashedValue(keyBuilder, this.getImageId());
        appendHashedValue(keyBuilder, this.getImageUrl());

        // 分页参数 - 使用默认值简化处理
        keyBuilder.append(Optional.ofNullable(this.getPage()).orElse(1)).append(":");
        keyBuilder.append(Optional.ofNullable(this.getPageSize()).orElse(20)).append(":");

        // 其他过滤参数 - 链式处理
        appendOptionalParam(keyBuilder, "cat", this.getCategoryId());
        appendPriceRange(keyBuilder);
        appendSortParams(keyBuilder);

        return keyBuilder.toString();
    }

    /**
     * 添加哈希化的值到缓存键
     */
    private void appendHashedValue(StringBuilder keyBuilder, String value) {
        String hashedValue = Optional.ofNullable(value)
          .map(DigestUtil::md5Hex)
          .orElse("");
        keyBuilder.append(hashedValue).append(":");
    }

    /**
     * 添加可选参数到缓存键
     */
    private void appendOptionalParam(StringBuilder keyBuilder, String prefix, Object value) {
        Optional.ofNullable(value)
          .ifPresent(v -> keyBuilder.append(prefix).append(":").append(v).append(":"));
    }

    /**
     * 添加价格范围参数
     */
    private void appendPriceRange(StringBuilder keyBuilder) {
        if (this.getMinPrice() != null || this.getMaxPrice() != null) {
            keyBuilder.append("price:")
              .append(Optional.ofNullable(this.getMinPrice()).orElse(""))
              .append("-")
              .append(Optional.ofNullable(this.getMaxPrice()).orElse(""))
              .append(":");
        }
    }

    /**
     * 添加排序参数
     */
    private void appendSortParams(StringBuilder keyBuilder) {
        Optional.ofNullable(this.getSortField())
          .filter(StringUtils::hasText)
          .ifPresent(sortField -> keyBuilder.append("sort:")
            .append(sortField)
            .append(":")
            .append(Optional.ofNullable(this.getSortOrder()).orElse("asc"))
            .append(":"));
    }

    /**
     * 搜索类型
     */
    @Getter
    public enum SearchType {

        KEYWORD(1),
        IMAGE(2);

        @EnumValue
        @JsonValue
        private final Integer value;

        SearchType(Integer value) {
            this.value = value;
        }

        @JsonCreator
        public static SearchType fromValue(Integer value) {
            if (value == null) {
                return KEYWORD; // 默认值
            }
            for (SearchType type : SearchType.values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            return KEYWORD; // 如果找不到匹配的值，返回默认值
        }
    }

}
