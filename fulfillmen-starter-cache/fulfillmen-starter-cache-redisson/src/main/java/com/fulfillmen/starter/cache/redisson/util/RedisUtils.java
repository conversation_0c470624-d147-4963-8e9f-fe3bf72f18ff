/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.cache.redisson.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fulfillmen.starter.core.constant.StringConstants;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.GeoPosition;
import org.redisson.api.GeoUnit;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBatch;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RBucket;
import org.redisson.api.RFuture;
import org.redisson.api.RList;
import org.redisson.api.RListAsync;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RQueue;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RScript;
import org.redisson.api.RSet;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.MessageListener;

/**
 * Redis 工具类 - 基于 Redisson 的全功能 Redis 操作工具类
 *
 * <p>
 * 本工具类基于 Redisson 客户端实现，提供了丰富的 Redis 操作功能，包括： 基础缓存操作、消息队列、发布订阅、分布式锁、布隆过滤器、地理位置、
 * 限流控制、分布式同步工具、Lua脚本执行等高级功能。
 * </p>
 *
 * <p>
 * <strong>主要功能模块：</strong>
 * </p>
 * <ul>
 * <li><strong>基础缓存操作</strong> - set/get/delete/expire等基本缓存操作</li>
 * <li><strong>数据结构操作</strong> - Hash/Set/ZSet/List等Redis数据结构操作</li>
 * <li><strong>消息队列</strong> - 基于Redis Queue的消息队列功能</li>
 * <li><strong>发布订阅</strong> - Redis的Pub/Sub消息模式</li>
 * <li><strong>分布式锁</strong> - 高性能分布式锁，支持自动续期</li>
 * <li><strong>布隆过滤器</strong> - 大数据去重和缓存穿透防护</li>
 * <li><strong>限流控制</strong> - 基于令牌桶算法的分布式限流</li>
 * <li><strong>地理位置</strong> - GEO地理位置计算和附近查询</li>
 * <li><strong>分布式同步</strong> - CountDownLatch/Semaphore等同步工具</li>
 * <li><strong>Lua脚本</strong> - 支持原子性操作的Lua脚本执行</li>
 * <li><strong>异步操作</strong> - 支持异步和批量操作，提升性能</li>
 * <li><strong>监控统计</strong> - 内置操作统计和性能监控功能</li>
 * </ul>
 *
 * <p>
 * <strong>核心特性：</strong>
 * </p>
 * <ul>
 * <li><strong>线程安全</strong> - 所有方法都是线程安全的</li>
 * <li><strong>高性能</strong> - 基于Redisson的高性能实现</li>
 * <li><strong>易于使用</strong> - 简洁的API设计，一行代码完成复杂操作</li>
 * <li><strong>监控友好</strong> - 内置操作统计，便于性能监控</li>
 * <li><strong>异常安全</strong> - 完善的异常处理和容错机制</li>
 * <li><strong>参数验证</strong> - 严格的参数校验，避免运行时错误</li>
 * </ul>
 *
 * <pre>
 * <strong>基础使用示例：</strong>
 *
 * // 1. 基础缓存操作
 * RedisUtils.set("user:123", userInfo, Duration.ofHours(1));
 * Optional&lt;User&gt; user = RedisUtils.get("user:123");
 *
 * // 2. 防缓存穿透的智能缓存
 * Optional&lt;User&gt; userProfile = RedisUtils.getOrLoad(
 * "profile:" + userId,
 * () -> userService.loadFromDatabase(userId),
 * Duration.ofMinutes(30)
 * );
 *
 * // 3. 分布式锁
 * String lockKey = "order:lock:" + orderId;
 * if (RedisUtils.tryLock(lockKey, 30000, 5000)) {
 * try {
 * // 处理订单业务逻辑
 * processOrder(orderId);
 * } finally {
 * RedisUtils.unlock(lockKey);
 * }
 * }
 *
 * // 4. 消息队列
 * RedisUtils.enqueue("email:queue", emailTask);
 * Optional&lt;EmailTask&gt; task = RedisUtils.dequeue("email:queue", 30);
 *
 * // 5. 限流控制
 * if (RedisUtils.rateLimit("api:" + userId, RateType.OVERALL, 100, 3600)) {
 * // 处理API请求
 * } else {
 * throw new TooManyRequestsException("请求过于频繁");
 * }
 *
 * // 6. 布隆过滤器
 * RedisUtils.initBloomFilter("user:filter", 1000000, 0.001);
 * RedisUtils.bloomAdd("user:filter", userId);
 * if (RedisUtils.bloomContains("user:filter", userId)) {
 * // 用户可能存在，查询数据库
 * }
 *
 * // 7. 地理位置
 * RedisUtils.geoAdd("stores", 116.3974, 39.9093, "store:001");
 * Double distance = RedisUtils.geoDistance("stores", "store:001", "store:002");
 *
 * // 8. Lua脚本原子操作
 * String script = "return redis.call('INCRBY', KEYS[1], ARGV[1])";
 * Long result = RedisUtils.executeScript(script, Arrays.asList("counter"), 5);
 * </pre>
 *
 * <p>
 * <strong>高级功能示例：</strong>
 * </p>
 *
 * <pre>
 * // 批量操作
 * Map&lt;String, Object&gt; batchData = new HashMap&lt;&gt;();
 * batchData.put("key1", "value1");
 * batchData.put("key2", "value2");
 * RedisUtils.setBatch(batchData, Duration.ofHours(1));
 *
 * // 异步操作
 * CompletableFuture&lt;Void&gt; future = RedisUtils.setAsync("async:key", data, Duration.ofMinutes(10));
 *
 * // 分布式同步
 * RedisUtils.createCountDownLatch("task:latch", 3);
 * // 在不同线程中
 * RedisUtils.countDownLatch("task:latch");
 * // 等待所有任务完成
 * RedisUtils.awaitCountDownLatch("task:latch", 60);
 *
 * // 缓存预热
 * Map&lt;String, Object&gt; warmupData = loadWarmupData();
 * RedisUtils.warmupCache(warmupData, Duration.ofHours(2));
 *
 * // 性能监控
 * Map&lt;String, OperationStats&gt; stats = RedisUtils.getOperationStats();
 * stats.forEach((op, stat) -> {
 * log.info("操作: {}, 次数: {}, 错误: {}, 平均耗时: {}ms",
 * op, stat.getOperationCount(), stat.getErrorCount(), stat.getAverageTime());
 * });
 * </pre>
 *
 * <p>
 * <strong>配置和依赖：</strong>
 * </p>
 * <ul>
 * <li>需要在Spring容器中配置RedissonClient Bean</li>
 * <li>依赖redisson-spring-boot-starter</li>
 * <li>支持单机、集群、哨兵模式</li>
 * <li>自动配置序列化器和编码器</li>
 * </ul>
 *
 * <p>
 * <strong>注意事项：</strong>
 * </p>
 * <ul>
 * <li>所有键名建议使用英文和冒号分隔，如: "user:profile:123"</li>
 * <li>合理设置过期时间，避免内存泄漏</li>
 * <li>大批量操作建议使用分片处理，避免阻塞Redis</li>
 * <li>Lua脚本要避免复杂逻辑，保证原子性</li>
 * <li>分布式锁要在finally块中释放，避免死锁</li>
 * <li>布隆过滤器一旦初始化，参数不能修改</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.2.0
 * @see org.redisson.api.RedissonClient
 * @see org.redisson.api.RBucket
 * @see org.redisson.api.RLock
 * @see org.redisson.api.RBloomFilter
 * @since 1.0.0
 */
@Slf4j
public final class RedisUtils {

    private static final RedissonClient CLIENT = SpringUtil.getBean(RedissonClient.class);

    /**
     * 监控和统计
     */
    private static final Map<String, AtomicLong> OPERATION_COUNTERS = new ConcurrentHashMap<>();
    private static final Map<String, AtomicLong> ERROR_COUNTERS = new ConcurrentHashMap<>();
    private static final Map<String, LongAdder> TIMING_STATS = new ConcurrentHashMap<>();

    private RedisUtils() {
    }

    /**
     * 记录操作统计
     *
     * @param operation 操作名称
     * @param startTime 开始时间
     */
    private static void recordOperation(String operation, long startTime) {
        OPERATION_COUNTERS.computeIfAbsent(operation, k -> new AtomicLong()).incrementAndGet();
        TIMING_STATS.computeIfAbsent(operation, k -> new LongAdder()).add(System.currentTimeMillis() - startTime);
    }

    /**
     * 获取 RedissonClient 实例
     *
     * <p>
     * 返回当前工具类使用的 RedissonClient 实例，供高级用户进行自定义操作。 该实例是线程安全的，可以在多线程环境中安全使用。
     * </p>
     *
     * <pre>
     * 使用案例：
     *
     * // 1. 获取客户端进行自定义操作
     * RedissonClient client = RedisUtils.getClient();
     *
     * // 2. 使用未封装的Redis操作
     * RHyperLogLog&lt;String&gt; hyperLogLog = client.getHyperLogLog("unique:visitors");
     * hyperLogLog.add("user1", "user2", "user3");
     * long uniqueCount = hyperLogLog.count();
     *
     * // 3. 使用BitSet进行位操作
     * RBitSet bitSet = client.getBitSet("user:online");
     * bitSet.set(userId);
     * boolean isOnline = bitSet.get(userId);
     *
     * // 4. 使用Redis Stream
     * RStream&lt;String, String&gt; stream = client.getStream("events");
     * stream.add("event", "user_login");
     *
     * // 5. 使用Redis Search（如果支持）
     * RSearch search = client.getSearch();
     * // 执行搜索操作
     *
     * // 6. 集群模式下的特殊操作
     * if (client.getConfig().isClusterConfig()) {
     * RClusterNodesGroup nodesGroup = client.getClusterNodesGroup();
     * // 集群特定操作
     * }
     *
     * // 7. 监控和统计
     * RCountDownLatch latch = client.getCountDownLatch("deployment:latch");
     * latch.trySetCount(serverCount);
     *
     * // 8. 分布式集合操作
     * RSet&lt;String&gt; distributedSet = client.getSet("global:tags");
     * distributedSet.add("tag1", "tag2");
     *
     * // 9. 高级锁操作
     * RLock multiLock = client.getMultiLock(
     * client.getLock("lock1"),
     * client.getLock("lock2")
     * );
     * multiLock.lock();
     * try {
     * // 需要多个锁的操作
     * } finally {
     * multiLock.unlock();
     * }
     *
     * // 10. 事务操作
     * RTransaction transaction = client.createTransaction(TransactionOptions.defaults());
     * RBucket&lt;String&gt; bucket1 = transaction.getBucket("key1");
     * RBucket&lt;String&gt; bucket2 = transaction.getBucket("key2");
     * bucket1.set("value1");
     * bucket2.set("value2");
     * transaction.commit();
     * </pre>
     *
     * <p>
     * <strong>应用场景：</strong>
     * </p>
     * <ul>
     * <li><strong>高级数据结构</strong> - HyperLogLog、BitSet、Stream等</li>
     * <li><strong>集群操作</strong> - 集群模式下的特殊操作</li>
     * <li><strong>事务处理</strong> - Redis事务操作</li>
     * <li><strong>监控统计</strong> - 获取Redis状态信息</li>
     * <li><strong>自定义扩展</strong> - 工具类未封装的功能</li>
     * <li><strong>原生API调用</strong> - 直接使用Redisson原生API</li>
     * </ul>
     *
     * <p>
     * <strong>线程安全性：</strong>
     * </p>
     * <ul>
     * <li>RedissonClient实例是线程安全的</li>
     * <li>可以在多线程环境中安全使用</li>
     * <li>所有通过该实例创建的Redis对象都是线程安全的</li>
     * <li>支持高并发场景</li>
     * </ul>
     *
     * <p>
     * <strong>注意事项：</strong>
     * </p>
     * <ul>
     * <li>返回的是全局单例实例，请勿关闭或修改配置</li>
     * <li>使用完毕后无需手动释放资源</li>
     * <li>建议优先使用工具类封装的方法</li>
     * <li>复杂操作建议在业务层进行封装</li>
     * </ul>
     *
     * @return RedissonClient实例，线程安全的全局单例
     * @since 1.2.0
     */
    public static RedissonClient getClient() {
        return CLIENT;
    }

    /**
     * 记录错误统计
     *
     * @param operation 操作名称
     */
    private static void recordError(String operation) {
        ERROR_COUNTERS.computeIfAbsent(operation, k -> new AtomicLong()).incrementAndGet();
    }

    /**
     * 获取操作统计信息
     *
     * @return 统计信息
     */
    public static Map<String, OperationStats> getOperationStats() {
        Map<String, OperationStats> stats = new HashMap<>();
        OPERATION_COUNTERS.forEach((operation, counter) -> {
            long count = counter.get();
            long errors = ERROR_COUNTERS.getOrDefault(operation, new AtomicLong()).get();
            LongAdder timing = TIMING_STATS.get(operation);
            double avgTime = timing != null ? (double)timing.sum() / count : 0.0;

            stats.put(operation, new OperationStats(count, errors, avgTime));
        });
        return Collections.unmodifiableMap(stats);
    }

    /**
     * 重置统计信息
     */
    public static void resetStats() {
        OPERATION_COUNTERS.clear();
        ERROR_COUNTERS.clear();
        TIMING_STATS.clear();
    }

    /**
     * 执行带统计的操作
     *
     * @param operation 操作名称
     * @param supplier  操作逻辑
     * @return 操作结果
     */
    private static <T> T executeWithStats(String operation, Supplier<T> supplier) {
        long startTime = System.currentTimeMillis();
        try {
            T result = supplier.get();
            recordOperation(operation, startTime);
            return result;
        } catch (Exception e) {
            recordError(operation);
            throw e;
        }
    }

    /**
     * 执行带统计的操作（无返回值）
     *
     * @param operation 操作名称
     * @param runnable  操作逻辑
     */
    private static void executeWithStats(String operation, Runnable runnable) {
        long startTime = System.currentTimeMillis();
        try {
            runnable.run();
            recordOperation(operation, startTime);
        } catch (Exception e) {
            recordError(operation);
            throw e;
        }
    }

    /**
     * 设置缓存
     *
     * @param key   键
     * @param value 值
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 基本用法 - 存储字符串
     *                                  RedisUtils.set("user:name", "张三");
     *
     *                                  // 存储对象
     *                                  User user = new User("张三", 25);
     *                                  RedisUtils.set("user:123", user);
     *
     *                                  // 存储数字
     *                                  RedisUtils.set("counter", 100);
     *
     *                                  // 存储布尔值
     *                                  RedisUtils.set("feature:enabled", true);
     *
     *                                  注意：
     *                                  - 这个方法设置的缓存没有过期时间，会永久保存
     *                                  - 如果需要设置过期时间，请使用 set(key, value, duration) 方法
     *                                  - 支持所有可序列化的对象类型
     *                                  </pre>
     */
    public static <T> void set(String key, T value) {
        validateKey(key);
        executeWithStats("set", () -> CLIENT.getBucket(key).set(value));
    }

    /**
     * 设置缓存
     * <pre>
     * 使用案例：
     *
     * // 设置缓存，5分钟后过期
     * RedisUtils.set("session:abc123", userSession, Duration.ofMinutes(5));
     *
     * // 设置缓存，1小时后过期
     * RedisUtils.set("user:profile:123", userProfile, Duration.ofHours(1));
     *
     * // 设置缓存，30秒后过期
     * RedisUtils.set("sms:code:13812345678", "123456", Duration.ofSeconds(30));
     *
     * // 设置缓存，1天后过期
     * RedisUtils.set("daily:report", report, Duration.ofDays(1));
     *
     * 常用过期时间：
     * Duration.ofSeconds(30) // 30秒
     * Duration.ofMinutes(5) // 5分钟
     * Duration.ofHours(1) // 1小时
     * Duration.ofDays(1) // 1天
     * Duration.ofDays(7) // 7天
     *
     * 注意：过期时间必须大于0，否则会抛出异常
     * </pre>
     * 
     * @param key      键
     * @param value    值
     * @param duration 过期时间
     * @throws IllegalArgumentException 如果key为null或duration为null
     *
     * 
     */
    public static <T> void set(String key, T value, Duration duration) {
        validateKey(key);
        Objects.requireNonNull(duration, "duration must not be null");
        executeWithStats("set", () -> CLIENT.getBucket(key).set(value, duration));
    }

    /**
     * 查询指定缓存
     *
     * @param key 键
     * @return 值的Optional包装
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 基本用法 - 获取字符串
     *                                  Optional&lt;String&gt; name = RedisUtils.get("user:name");
     *                                  if (name.isPresent()) {
     *                                  System.out.println("用户名: " + name.get());
     *                                  }
     *
     *                                  // 获取对象
     *                                  Optional&lt;User&gt; user = RedisUtils.get("user:123");
     *                                  User userInfo = user.orElse(new User("默认用户", 0));
     *
     *                                  // 获取数字
     *                                  Optional&lt;Integer&gt; counter = RedisUtils.get("counter");
     *                                  int count = counter.orElse(0);
     *
     *                                  // 链式调用
     *                                  String userName = RedisUtils.&lt;String&gt;get("user:name")
     *                                  .orElse("匿名用户");
     *
     *                                  // 判断是否存在
     *                                  if (RedisUtils.get("session:abc123").isPresent()) {
     *                                  // 会话有效
     *                                  }
     *
     *                                  注意：
     *                                  - 返回 Optional 避免空指针异常
     *                                  - 如果缓存不存在，返回 Optional.empty()
     *                                  - 可以使用 orElse() 提供默认值
     *                                  </pre>
     */
    public static <T> Optional<T> get(String key) {
        validateKey(key);
        return executeWithStats("get", () -> {
            RBucket<T> bucket = CLIENT.getBucket(key);
            return Optional.ofNullable(bucket.get());
        });
    }

    /**
     * 设置缓存（List 集合）
     *
     * @param key   键
     * @param value 值
     * @throws IllegalArgumentException 如果key为null或value为null
     */
    public static <T> void setList(String key, List<T> value) {
        validateKey(key);
        Objects.requireNonNull(value, "value must not be null");
        RList<T> list = CLIENT.getList(key);
        list.addAll(value);
    }

    /**
     * 设置缓存（List 集合）
     *
     * @param key      键
     * @param value    值
     * @param duration 过期时间
     * @throws IllegalArgumentException 如果key为null或value为null或duration为null
     */
    public static <T> void setList(String key, List<T> value, Duration duration) {
        validateKey(key);
        Objects.requireNonNull(value, "value must not be null");
        Objects.requireNonNull(duration, "duration must not be null");

        try {
            RBatch batch = CLIENT.createBatch();
            RListAsync<T> list = batch.getList(key);
            list.addAllAsync(value);
            list.expireAsync(duration);
            batch.execute();
        } catch (Exception e) {
            log.error("Failed to set list with key: {}", key, e);
            throw new RuntimeException("Failed to set list", e);
        }
    }

    /**
     * 查询指定缓存（List 集合）
     *
     * @param key 键
     * @return 值列表的Optional包装
     * @throws IllegalArgumentException 如果key为null
     */
    public static <T> Optional<List<T>> getList(String key) {
        validateKey(key);
        RList<T> list = CLIENT.getList(key);
        return Optional.ofNullable(list.readAll());
    }

    /**
     * 删除缓存
     *
     * @param key 键
     * @return true：设置成功；false：设置失败
     */
    public static boolean delete(String key) {
        return executeWithStats("delete", () -> CLIENT.getBucket(key).delete());
    }

    /**
     * 删除缓存
     *
     * @param pattern 键模式
     */
    public static void deleteByPattern(String pattern) {
        CLIENT.getKeys().deleteByPattern(pattern);
    }

    /**
     * 递增 1
     *
     * @param key 键
     * @return 当前值
     */
    public static long incr(String key) {
        return CLIENT.getAtomicLong(key).incrementAndGet();
    }

    /**
     * 获取当前值
     *
     * @param key 键
     * @return 当前值
     */
    public static long getCounter(String key) {
        return CLIENT.getAtomicLong(key).get();
    }

    /**
     * 递减 1
     *
     * @param key 键
     * @return 当前值
     */
    public static long decr(String key) {
        return CLIENT.getAtomicLong(key).decrementAndGet();
    }

    /**
     * 设置缓存过期时间
     *
     * @param key      键
     * @param duration 过期时间
     * @return true：设置成功；false：设置失败
     */
    public static boolean expire(String key, Duration duration) {
        return CLIENT.getBucket(key).expire(duration);
    }

    /**
     * 查询缓存剩余过期时间
     *
     * @param key 键
     * @return 缓存剩余过期时间（单位：毫秒）
     */
    public static long getTimeToLive(String key) {
        return CLIENT.getBucket(key).remainTimeToLive();
    }

    /**
     * 是否存在指定缓存
     *
     * @param key 键
     * @return true：存在；false：不存在
     */
    public static boolean exists(String key) {
        return CLIENT.getKeys().countExists(key) > 0;
    }

    /**
     * 查询缓存列表
     *
     * @param pattern 键模式
     * @return 缓存列表
     */
    public static Collection<String> keys(String pattern) {
        return CLIENT.getKeys().getKeysStreamByPattern(pattern).toList();
    }

    /**
     * 添加元素到 ZSet 中
     *
     * @param key   键
     * @param value 值
     * @param score 分数
     * @return true：添加成功；false：添加失败
     */
    public static <T> boolean zAdd(String key, T value, double score) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.add(score, value);
    }

    /**
     * 查询 ZSet 中指定元素的分数
     *
     * @param key   键
     * @param value 值
     * @return 分数（null 表示元素不存在）
     */
    public static <T> Double zScore(String key, T value) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.getScore(value);
    }

    /**
     * 查询 ZSet 中指定元素的排名
     *
     * @param key   键
     * @param value 值
     * @return 排名（从 0 开始，null 表示元素不存在）
     */
    public static <T> Integer zRank(String key, T value) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.rank(value);
    }

    /**
     * 查询 ZSet 中的元素个数
     *
     * @param key 键
     * @return 元素个数
     */
    public static <T> int zSize(String key) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.size();
    }

    /**
     * 从 ZSet 中删除指定元素
     *
     * @param key   键
     * @param value 值
     * @return true：删除成功；false：删除失败
     */
    public static <T> boolean zRemove(String key, T value) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.remove(value);
    }

    /**
     * 删除 ZSet 中指定分数范围内的元素
     *
     * @param key 键
     * @param min 最小分数（包含）
     * @param max 最大分数（包含）
     * @return 删除的元素个数
     */
    public static <T> int zRemoveRangeByScore(String key, double min, double max) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.removeRangeByScore(min, true, max, true);
    }

    /**
     * 删除 ZSet 中指定排名范围内的元素
     *
     * <p>
     * 索引从 0 开始。<code>-1<code> 表示最高分，<code>-2<code> 表示第二高分。
     * </p>
     *
     * @param key        键
     * @param startIndex 起始索引
     * @param endIndex   结束索引
     * @return 删除的元素个数
     */
    public static <T> int zRemoveRangeByRank(String key, int startIndex, int endIndex) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.removeRangeByRank(startIndex, endIndex);
    }

    /**
     * 根据分数范围查询 ZSet 中的元素列表
     *
     * @param key 键
     * @param min 最小分数（包含）
     * @param max 最大分数（包含）
     * @return 元素列表
     */
    public static <T> Collection<T> zRangeByScore(String key, double min, double max) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.valueRange(min, true, max, true);
    }

    /**
     * 根据分数范围查询 ZSet 中的元素列表
     *
     * @param key    键
     * @param min    最小分数（包含）
     * @param max    最大分数（包含）
     * @param offset 偏移量
     * @param count  数量
     * @return 元素列表
     */
    public static <T> Collection<T> zRangeByScore(String key, double min, double max, int offset, int count) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.valueRange(min, true, max, true, offset, count);
    }

    /**
     * 根据分数范围查询 ZSet 中的元素个数
     *
     * @param key 键
     * @param min 最小分数（包含）
     * @param max 最大分数（包含）
     * @return 元素个数
     */
    public static <T> int zCountRangeByScore(String key, double min, double max) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        return zSet.count(min, true, max, true);
    }

    /**
     * 计算 ZSet 中多个元素的分数之和
     *
     * @param key    键
     * @param values 值列表
     * @return 分数之和
     */
    public static <T> double zSum(String key, Collection<T> values) {
        RScoredSortedSet<T> zSet = CLIENT.getScoredSortedSet(key);
        double sum = 0;
        for (T value : values) {
            Double score = zSet.getScore(value);
            if (score != null) {
                sum += score;
            }
        }
        return sum;
    }

    /**
     * 限流
     *
     * @param key          键
     * @param rateType     限流类型（OVERALL：全局限流；PER_CLIENT：单机限流）
     * @param rate         速率（指定时间间隔产生的令牌数）
     * @param rateInterval 速率间隔（时间间隔，单位：秒）
     * @return true：成功；false：失败
     *
     *         <pre>
     *         使用案例：
     *
     *         // API接口限流 - 每秒最多10个请求
     *         String apiKey = "api:limit:user:" + userId;
     *         if (RedisUtils.rateLimit(apiKey, RateType.OVERALL, 10, 1)) {
     *         // 处理API请求
     *         processApiRequest();
     *         } else {
     *         throw new TooManyRequestsException("请求过于频繁，请稍后再试");
     *         }
     *
     *         // 登录限流 - 每分钟最多5次登录尝试
     *         String loginKey = "login:limit:" + username;
     *         if (RedisUtils.rateLimit(loginKey, RateType.OVERALL, 5, 60)) {
     *         // 允许登录
     *         performLogin(username, password);
     *         } else {
     *         throw new SecurityException("登录尝试过于频繁，请1分钟后再试");
     *         }
     *
     *         // 发送短信限流 - 每分钟最多1条
     *         String smsKey = "sms:limit:" + phone;
     *         if (RedisUtils.rateLimit(smsKey, RateType.OVERALL, 1, 60)) {
     *         sendSmsCode(phone);
     *         } else {
     *         throw new BusinessException("短信发送过于频繁，请1分钟后再试");
     *         }
     *
     *         // 评论限流 - 每10秒最多1条评论
     *         String commentKey = "comment:limit:" + userId;
     *         if (RedisUtils.rateLimit(commentKey, RateType.OVERALL, 1, 10)) {
     *         saveComment(userId, content);
     *         } else {
     *         throw new BusinessException("评论过于频繁，请稍后再试");
     *         }
     *
     *         // 下载限流 - 每小时最多100次下载
     *         String downloadKey = "download:limit:" + userId;
     *         if (RedisUtils.rateLimit(downloadKey, RateType.OVERALL, 100, 3600)) {
     *         downloadFile(fileId);
     *         } else {
     *         throw new BusinessException("下载次数已达上限，请1小时后再试");
     *         }
     *
     *         // 在Controller中使用
     *         &#64;PostMapping("/api/data")
     *         public ResponseEntity&lt;?&gt; getData(HttpServletRequest request) {
     *         String clientIp = getClientIp(request);
     *         String rateLimitKey = "api:data:limit:" + clientIp;
     *
     *         if (!RedisUtils.rateLimit(rateLimitKey, RateType.OVERALL, 100, 3600)) {
     *         return ResponseEntity.status(429).body("请求过于频繁");
     *         }
     *
     *         return ResponseEntity.ok(dataService.getData());
     *         }
     *
     *         常用限流配置：
     *         - 短信验证码：1次/分钟
     *         - 登录尝试：5次/分钟
     *         - API调用：100次/小时
     *         - 评论发布：1次/10秒
     *         - 文件上传：10次/小时
     *
     *         应用场景：
     *         - API接口限流
     *         - 用户行为限流
     *         - 防刷接口
     *         - 资源访问控制
     *         - 系统保护
     *
     *         注意：
     *         - RateType.OVERALL：全局限流，所有实例共享
     *         - RateType.PER_CLIENT：单机限流，每个实例独立
     *         - 基于令牌桶算法实现
     *         - 限流粒度可根据需要调整（用户、IP、接口等）
     *         </pre>
     */
    public static boolean rateLimit(String key, RateType rateType, int rate, int rateInterval) {
        RRateLimiter rateLimiter = CLIENT.getRateLimiter(key);
        rateLimiter.trySetRate(rateType, rate, Duration.ofSeconds(rateInterval));
        return rateLimiter.tryAcquire(1);
    }

    /**
     * 尝试获取锁
     *
     * @param key        键
     * @param expireTime 锁过期时间（单位：毫秒）
     * @param timeout    获取锁超时时间（单位：毫秒）
     * @return true：成功；false：失败
     *
     *         <pre>
     *         使用案例：
     *
     *         // 基本用法 - 获取锁，锁过期时间30秒，等待时间5秒
     *         String lockKey = "order:process:" + orderId;
     *         boolean locked = RedisUtils.tryLock(lockKey, 30000, 5000);
     *         if (locked) {
     *         try {
     *         // 处理订单业务逻辑
     *         processOrder(orderId);
     *         } finally {
     *         RedisUtils.unlock(lockKey);
     *         }
     *         } else {
     *         log.warn("获取订单锁失败: {}", orderId);
     *         }
     *
     *         // 防重复提交
     *         String submitKey = "submit:" + userId + ":" + formId;
     *         if (RedisUtils.tryLock(submitKey, 5000, 1000)) {
     *         try {
     *         // 处理表单提交
     *         submitForm(formData);
     *         } finally {
     *         RedisUtils.unlock(submitKey);
     *         }
     *         } else {
     *         throw new BusinessException("请勿重复提交");
     *         }
     *
     *         // 定时任务防并发
     *         if (RedisUtils.tryLock("scheduled:task:cleanup", 60000, 0)) {
     *         try {
     *         cleanupTempFiles();
     *         } finally {
     *         RedisUtils.unlock("scheduled:task:cleanup");
     *         }
     *         }
     *
     *         // 库存扣减
     *         String stockKey = "stock:lock:" + productId;
     *         if (RedisUtils.tryLock(stockKey, 10000, 3000)) {
     *         try {
     *         if (checkStock(productId) >= quantity) {
     *         reduceStock(productId, quantity);
     *         }
     *         } finally {
     *         RedisUtils.unlock(stockKey);
     *         }
     *         }
     *
     *         应用场景：
     *         - 防止并发操作
     *         - 定时任务防重复执行
     *         - 库存扣减
     *         - 订单处理
     *         - 防重复提交
     *
     *         注意：
     *         - 必须在finally块中释放锁
     *         - 锁会自动过期，防止死锁
     *         - 获取锁失败时不要阻塞业务流程
     *         - 锁的粒度要合适，避免性能问题
     *         </pre>
     */
    public static boolean tryLock(String key, long expireTime, long timeout) {
        return tryLock(key, expireTime, timeout, TimeUnit.MILLISECONDS);
    }

    /**
     * 释放锁
     *
     * @param key 键
     * @return true：释放成功；false：释放失败
     */
    public static boolean unlock(String key) {
        RLock lock = getLock(key);
        if (lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
                return true;
            } catch (Exception e) {
                log.error("Failed to unlock: {}", key, e);
                return false;
            }
        }
        return false;
    }

    /**
     * 尝试获取锁
     *
     * @param key        键
     * @param expireTime 锁过期时间
     * @param timeout    获取锁超时时间
     * @param unit       时间单位
     * @return true：成功；false：失败
     */
    public static boolean tryLock(String key, long expireTime, long timeout, TimeUnit unit) {
        RLock lock = getLock(key);
        if (lock.isHeldByCurrentThread()) {
            return false;
        }
        try {
            return lock.tryLock(timeout, expireTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 获取锁实例
     *
     * @param key 键
     * @return 锁实例
     */
    public static RLock getLock(String key) {
        return CLIENT.getLock(key);
    }

    /**
     * 格式化键，将各子键用 : 拼接起来
     *
     * @param subKeys 子键列表
     * @return 键
     */
    public static String formatKey(String... subKeys) {
        return String.join(StringConstants.COLON, ArrayUtil.removeBlank(subKeys));
    }

    /**
     * 验证键的有效性
     *
     * @param key 键
     * @throws IllegalArgumentException 如果key为null或空
     */
    private static void validateKey(String key) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("key must not be null or empty");
        }
    }

    /**
     * 设置 Hash 的字段值
     *
     * @param key   键
     * @param field 字段
     * @param value 值
     * @throws IllegalArgumentException 如果key或field为null
     */
    public static <T> void hset(String key, String field, T value) {
        validateKey(key);
        Objects.requireNonNull(field, "field must not be null");
        CLIENT.getMap(key).put(field, value);
    }

    /**
     * Hash 操作
     */

    /**
     * 获取 Hash 的字段值
     *
     * @param key   键
     * @param field 字段
     * @return 值的Optional包装
     * @throws IllegalArgumentException 如果key或field为null
     */
    public static <T> Optional<T> hget(String key, String field) {
        validateKey(key);
        Objects.requireNonNull(field, "field must not be null");
        return Optional.ofNullable(CLIENT.<String, T>getMap(key).get(field));
    }

    /**
     * 删除 Hash 的字段
     *
     * @param key    键
     * @param fields 字段列表
     * @return 成功删除的字段数
     */
    public static long hdel(String key, String... fields) {
        validateKey(key);
        return CLIENT.getMap(key).fastRemove(fields);
    }

    /**
     * 获取 Hash 的所有字段
     *
     * @param key 键
     * @return 字段集合
     */
    public static Set<String> hkeys(String key) {
        validateKey(key);
        return CLIENT.<String, Object>getMap(key).keySet();
    }

    /**
     * 获取 Hash 的所有值
     *
     * @param key 键
     * @return 值集合
     */
    public static <T> Collection<T> hvals(String key) {
        validateKey(key);
        return CLIENT.<String, T>getMap(key).values();
    }

    /**
     * 获取 Hash 的所有字段值
     *
     * @param key 键
     * @return 字段值映射
     */
    public static <T> Map<String, T> hgetAll(String key) {
        validateKey(key);
        return CLIENT.<String, T>getMap(key).readAllMap();
    }

    /**
     * 添加元素到 Set
     *
     * @param key    键
     * @param values 值列表
     * @return 成功添加的元素数
     */
    public static <T> long sadd(String key, T... values) {
        validateKey(key);
        RSet<T> set = CLIENT.getSet(key);
        int sizeBefore = set.size();
        set.addAll(Arrays.asList(values));
        return set.size() - sizeBefore;
    }

    /**
     * Set 操作
     */

    /**
     * 获取 Set 中的所有元素
     *
     * @param key 键
     * @return 元素集合
     */
    public static <T> Set<T> smembers(String key) {
        validateKey(key);
        return CLIENT.<T>getSet(key).readAll();
    }

    /**
     * 判断元素是否是 Set 的成员
     *
     * @param key   键
     * @param value 值
     * @return true：是；false：否
     */
    public static <T> boolean sismember(String key, T value) {
        validateKey(key);
        return CLIENT.<T>getSet(key).contains(value);
    }

    /**
     * 获取 Set 的元素数量
     *
     * @param key 键
     * @return 元素数量
     */
    public static long scard(String key) {
        validateKey(key);
        return CLIENT.getSet(key).size();
    }

    /**
     * 从 Set 中随机获取元素
     *
     * @param key   键
     * @param count 数量
     * @return 随机元素集合
     */
    public static <T> Set<T> srandmember(String key, int count) {
        validateKey(key);
        return CLIENT.<T>getSet(key).random(count);
    }

    /**
     * 从 Set 中移除元素
     *
     * @param key    键
     * @param values 值列表
     * @return 成功移除的元素数
     */
    public static <T> long srem(String key, T... values) {
        validateKey(key);
        RSet<T> set = CLIENT.getSet(key);
        int sizeBefore = set.size();
        set.removeAll(Arrays.asList(values));
        return sizeBefore - set.size();
    }

    /**
     * 计算多个 Set 的交集
     *
     * @param keys 键列表
     * @return 交集元素集合
     */
    public static <T> Set<T> sinter(String... keys) {
        validateKeys(keys);
        RSet<T> firstSet = CLIENT.<T>getSet(keys[0]);
        if (keys.length == 1) {
            return firstSet.readAll();
        }

        try {
            String[] otherKeys = new String[keys.length - 1];
            for (int i = 1; i < keys.length; i++) {
                otherKeys[i - 1] = CLIENT.<T>getSet(keys[i]).getName();
            }
            return firstSet.readIntersection(otherKeys);
        } catch (Exception e) {
            throw new RuntimeException("Failed to compute set intersection", e);
        }
    }

    /**
     * 计算多个 Set 的并集
     *
     * @param keys 键列表
     * @return 并集元素集合
     */
    public static <T> Set<T> sunion(String... keys) {
        validateKeys(keys);
        RSet<T> firstSet = CLIENT.<T>getSet(keys[0]);
        if (keys.length == 1) {
            return firstSet.readAll();
        }

        try {
            String[] otherKeys = new String[keys.length - 1];
            for (int i = 1; i < keys.length; i++) {
                otherKeys[i - 1] = CLIENT.<T>getSet(keys[i]).getName();
            }
            return firstSet.readUnion(otherKeys);
        } catch (Exception e) {
            throw new RuntimeException("Failed to compute set union", e);
        }
    }

    /**
     * 计算多个 Set 的差集
     *
     * @param keys 键列表
     * @return 差集元素集合
     */
    public static <T> Set<T> sdiff(String... keys) {
        validateKeys(keys);
        RSet<T> firstSet = CLIENT.<T>getSet(keys[0]);
        if (keys.length == 1) {
            return firstSet.readAll();
        }

        try {
            String[] otherKeys = new String[keys.length - 1];
            for (int i = 1; i < keys.length; i++) {
                otherKeys[i - 1] = CLIENT.<T>getSet(keys[i]).getName();
            }
            return firstSet.readDiff(otherKeys);
        } catch (Exception e) {
            throw new RuntimeException("Failed to compute set difference", e);
        }
    }

    /**
     * 验证多个键的有效性
     *
     * @param keys 键列表
     * @throws IllegalArgumentException 如果任意键为null或空
     */
    private static void validateKeys(String... keys) {
        Objects.requireNonNull(keys, "keys must not be null");
        if (keys.length == 0) {
            throw new IllegalArgumentException("keys must not be empty");
        }
        for (String key : keys) {
            validateKey(key);
        }
    }

    /**
     * 异步设置缓存
     *
     * @param key   键
     * @param value 值
     * @return 异步执行结果
     * @throws IllegalArgumentException 如果key为null
     */
    public static <T> CompletableFuture<Void> setAsync(String key, T value) {
        validateKey(key);
        RFuture<Void> future = CLIENT.getBucket(key).setAsync(value);
        return CompletableFuture.supplyAsync(() -> {
            try {
                future.get();
                return null;
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        });
    }

    /**
     * 异步操作
     */

    /**
     * 异步设置缓存（带过期时间）
     *
     * @param key      键
     * @param value    值
     * @param duration 过期时间
     * @return 异步执行结果
     * @throws IllegalArgumentException 如果key为null或duration为null
     */
    public static <T> CompletableFuture<Void> setAsync(String key, T value, Duration duration) {
        validateKey(key);
        Objects.requireNonNull(duration, "duration must not be null");
        RFuture<Void> future = CLIENT.getBucket(key).setAsync(value, duration);
        return CompletableFuture.supplyAsync(() -> {
            try {
                future.get();
                return null;
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        });
    }

    /**
     * 异步获取缓存
     *
     * @param key 键
     * @return 异步执行结果
     * @throws IllegalArgumentException 如果key为null
     */
    public static <T> CompletableFuture<Optional<T>> getAsync(String key) {
        validateKey(key);
        RFuture<T> future = CLIENT.<T>getBucket(key).getAsync();
        return CompletableFuture.supplyAsync(() -> {
            try {
                return Optional.ofNullable(future.get());
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        });
    }

    /**
     * 异步删除缓存
     *
     * @param key 键
     * @return 异步执行结果
     */
    public static CompletableFuture<Boolean> deleteAsync(String key) {
        RFuture<Boolean> future = CLIENT.getBucket(key).deleteAsync();
        return CompletableFuture.supplyAsync(() -> {
            try {
                return future.get();
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        });
    }

    /**
     * 异步设置过期时间
     *
     * @param key      键
     * @param duration 过期时间
     * @return 异步执行结果
     */
    public static CompletableFuture<Boolean> expireAsync(String key, Duration duration) {
        RFuture<Boolean> future = CLIENT.getBucket(key).expireAsync(duration);
        return CompletableFuture.supplyAsync(() -> {
            try {
                return future.get();
            } catch (Exception e) {
                throw new CompletionException(e);
            }
        });
    }

    /**
     * 批量设置缓存
     *
     * @param map      键值映射
     * @param duration 过期时间（可选）
     */
    public static <T> void setBatch(Map<String, T> map, Duration duration) {
        Objects.requireNonNull(map, "map must not be null");
        if (map.isEmpty()) {
            return;
        }

        RBatch batch = CLIENT.createBatch();
        map.forEach((key, value) -> {
            validateKey(key);
            if (duration != null) {
                batch.getBucket(key).setAsync(value, duration);
            } else {
                batch.getBucket(key).setAsync(value);
            }
        });
        batch.execute();
    }

    /**
     * 批量操作
     */

    /**
     * 批量获取缓存
     *
     * @param keys 键列表
     * @return 键值映射
     */
    public static <T> Map<String, T> getBatch(Collection<String> keys) {
        Objects.requireNonNull(keys, "keys must not be null");
        if (keys.isEmpty()) {
            return Collections.emptyMap();
        }

        RBatch batch = CLIENT.createBatch();
        Map<String, RFuture<T>> futures = new HashMap<>(keys.size());

        keys.forEach(key -> {
            validateKey(key);
            futures.put(key, batch.<T>getBucket(key).getAsync());
        });

        batch.execute();

        return futures.entrySet().stream().filter(entry -> {
            try {
                return entry.getValue().get() != null;
            } catch (Exception e) {
                log.error("Failed to get value for key: {}", entry.getKey(), e);
                return false;
            }
        }).collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            try {
                return entry.getValue().get();
            } catch (Exception e) {
                return null;
            }
        }));
    }

    /**
     * 批量删除缓存
     *
     * @param keys 键列表
     * @return 成功删除的键数量
     */
    public static long deleteBatch(Collection<String> keys) {
        Objects.requireNonNull(keys, "keys must not be null");
        if (keys.isEmpty()) {
            return 0;
        }

        RBatch batch = CLIENT.createBatch();
        keys.forEach(key -> {
            validateKey(key);
            batch.getBucket(key).deleteAsync();
        });
        batch.execute();

        return keys.size();
    }

    /**
     * 分片处理大批量操作
     *
     * @param keys      键列表
     * @param batchSize 每批处理的大小
     * @param consumer  处理逻辑
     */
    public static <T> void processBatchWithSharding(Collection<String> keys,
                                                    int batchSize,
                                                    Consumer<Collection<String>> consumer) {
        Objects.requireNonNull(keys, "keys must not be null");
        Objects.requireNonNull(consumer, "consumer must not be null");
        if (batchSize <= 0) {
            throw new IllegalArgumentException("batchSize must be positive");
        }

        List<String> keyList = new ArrayList<>(keys);
        for (int i = 0; i < keyList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, keyList.size());
            List<String> batch = keyList.subList(i, end);
            consumer.accept(batch);
        }
    }

    /**
     * 向队列中添加消息
     *
     * @param queueName 队列名称
     * @param message   消息内容
     * @return true：添加成功；false：添加失败
     *
     *         <pre>
     *         使用案例：
     *
     *         // 添加字符串消息
     *         boolean success = RedisUtils.enqueue("email:queue", "发送邮件给用户A");
     *
     *         // 添加对象消息
     *         OrderMessage order = new OrderMessage(orderId, userId, amount);
     *         RedisUtils.enqueue("order:processing", order);
     *
     *         // 添加JSON消息
     *         String jsonMessage = "{\"type\":\"notification\",\"content\":\"新消息\"}";
     *         RedisUtils.enqueue("notifications", jsonMessage);
     *
     *         // 批量添加消息
     *         List&lt;String&gt; messages = Arrays.asList("msg1", "msg2", "msg3");
     *         messages.forEach(msg -> RedisUtils.enqueue("batch:queue", msg));
     *
     *         应用场景：
     *         - 异步任务处理
     *         - 邮件发送队列
     *         - 订单处理队列
     *         - 日志收集队列
     *         - 消息通知队列
     *
     *         注意：
     *         - 队列是FIFO（先进先出）的
     *         - 消息不会过期，除非被消费
     *         - 支持所有可序列化的对象类型
     *         </pre>
     */
    public static <T> boolean enqueue(String queueName, T message) {
        validateKey(queueName);
        Objects.requireNonNull(message, "message must not be null");
        return CLIENT.getQueue(queueName).offer(message);
    }

    /**
     * 消息队列操作
     */

    /**
     * 从队列中取出消息（阻塞）
     *
     * @param queueName 队列名称
     * @param timeout   超时时间（秒）
     * @return 消息内容，超时返回null
     *
     *         <pre>
     *         使用案例：
     *
     *         // 基本用法 - 阻塞等待消息，最多等待30秒
     *         Optional&lt;String&gt; message = RedisUtils.dequeue("email:queue", 30);
     *         if (message.isPresent()) {
     *         System.out.println("处理邮件: " + message.get());
     *         }
     *
     *         // 消费者模式 - 持续消费消息
     *         while (true) {
     *         Optional&lt;OrderMessage&gt; order = RedisUtils.dequeue("order:processing", 10);
     *         if (order.isPresent()) {
     *         processOrder(order.get());
     *         } else {
     *         // 10秒内没有消息，可以做其他事情
     *         Thread.sleep(1000);
     *         }
     *         }
     *
     *         // 多线程消费者
     *         &#64;Async
     *         public void consumeMessages() {
     *         Optional&lt;String&gt; message = RedisUtils.dequeue("notifications", 60);
     *         message.ifPresent(this::sendNotification);
     *         }
     *
     *         // 带超时处理
     *         Optional&lt;String&gt; msg = RedisUtils.dequeue("critical:queue", 5);
     *         if (msg.isEmpty()) {
     *         log.warn("5秒内没有接收到关键消息");
     *         }
     *
     *         应用场景：
     *         - 后台任务处理
     *         - 工作队列模式
     *         - 生产者-消费者模式
     *         - 异步任务调度
     *
     *         注意：
     *         - 这是阻塞方法，会等待指定时间
     *         - 如果队列为空，会阻塞直到有消息或超时
     *         - 超时时间为0表示立即返回
     *         - 线程可能被中断，需要处理InterruptedException
     *         </pre>
     */
    public static <T> Optional<T> dequeue(String queueName, int timeout) {
        validateKey(queueName);
        try {
            T message = CLIENT.<T>getBlockingQueue(queueName).poll(timeout, TimeUnit.SECONDS);
            return Optional.ofNullable(message);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Optional.empty();
        }
    }

    /**
     * 从队列中取出消息（非阻塞）
     *
     * @param queueName 队列名称
     * @return 消息内容，队列为空返回null
     */
    public static <T> Optional<T> dequeueNonBlocking(String queueName) {
        validateKey(queueName);
        return Optional.ofNullable(CLIENT.<T>getQueue(queueName).poll());
    }

    /**
     * 获取队列大小
     *
     * @param queueName 队列名称
     * @return 队列大小
     */
    public static int getQueueSize(String queueName) {
        validateKey(queueName);
        return CLIENT.getQueue(queueName).size();
    }

    /**
     * 发布消息到主题
     *
     * @param topicName 主题名称
     * @param message   消息内容
     * @return 接收到消息的订阅者数量
     */
    public static <T> long publish(String topicName, T message) {
        validateKey(topicName);
        Objects.requireNonNull(message, "message must not be null");
        return CLIENT.getTopic(topicName).publish(message);
    }

    /**
     * 发布/订阅操作
     */

    /**
     * 订阅主题（字符串类型消息）
     *
     * @param topicName 主题名称
     * @param listener  消息监听器
     * @return 监听器ID
     */
    public static int subscribeString(String topicName, Consumer<String> listener) {
        validateKey(topicName);
        Objects.requireNonNull(listener, "listener must not be null");
        return CLIENT.getTopic(topicName).addListener(String.class, new MessageListener<String>() {
            @Override
            public void onMessage(CharSequence channel, String msg) {
                listener.accept(msg);
            }
        });
    }

    /**
     * 取消订阅
     *
     * @param topicName  主题名称
     * @param listenerId 监听器ID
     */
    public static void unsubscribe(String topicName, int listenerId) {
        validateKey(topicName);
        CLIENT.getTopic(topicName).removeListener(listenerId);
    }

    /**
     * 初始化布隆过滤器
     *
     * @param filterName        过滤器名称
     * @param expectedElements  预期元素数量
     * @param falsePositiveRate 误判率
     * @return true：初始化成功；false：初始化失败
     *
     *         <pre>
     *         使用案例：
     *
     *         // 初始化用户ID过滤器，预期100万用户，误判率0.01%
     *         boolean success = RedisUtils.initBloomFilter("user:ids", 1000000, 0.0001);
     *         if (success) {
     *         log.info("用户ID布隆过滤器初始化成功");
     *         }
     *
     *         // 初始化邮箱过滤器，预期50万邮箱，误判率0.1%
     *         RedisUtils.initBloomFilter("email:filter", 500000, 0.001);
     *
     *         // 初始化IP黑名单过滤器，预期1万IP，误判率0.05%
     *         RedisUtils.initBloomFilter("ip:blacklist", 10000, 0.0005);
     *
     *         // 初始化商品ID过滤器，预期10万商品，误判率0.01%
     *         RedisUtils.initBloomFilter("product:ids", 100000, 0.0001);
     *
     *         常用参数配置：
     *         - 预期元素数量：根据业务实际需求估算
     *         - 误判率：0.001（0.1%）- 0.0001（0.01%）
     *         - 误判率越低，占用内存越大
     *
     *         应用场景：
     *         - 缓存穿透防护
     *         - 垃圾邮件过滤
     *         - 黑名单检查
     *         - 重复数据检测
     *         - 大数据去重
     *
     *         注意：
     *         - 布隆过滤器不支持删除操作
     *         - 可能存在误判（false positive），但不会漏判（false negative）
     *         - 一旦初始化，参数不能修改
     *         - 误判率和内存使用量成反比
     *         </pre>
     */
    public static boolean initBloomFilter(String filterName, long expectedElements, double falsePositiveRate) {
        validateKey(filterName);
        if (expectedElements <= 0) {
            throw new IllegalArgumentException("expectedElements must be positive");
        }
        if (falsePositiveRate <= 0 || falsePositiveRate >= 1) {
            throw new IllegalArgumentException("falsePositiveRate must be between 0 and 1");
        }
        return CLIENT.getBloomFilter(filterName).tryInit(expectedElements, falsePositiveRate);
    }

    /**
     * 布隆过滤器操作
     */

    /**
     * 向布隆过滤器添加元素
     *
     * @param filterName 过滤器名称
     * @param element    元素
     * @return true：添加成功；false：添加失败
     */
    public static <T> boolean bloomAdd(String filterName, T element) {
        validateKey(filterName);
        Objects.requireNonNull(element, "element must not be null");
        return CLIENT.getBloomFilter(filterName).add(element);
    }

    /**
     * 检查元素是否在布隆过滤器中
     *
     * @param filterName 过滤器名称
     * @param element    元素
     * @return true：可能存在；false：一定不存在
     */
    public static <T> boolean bloomContains(String filterName, T element) {
        validateKey(filterName);
        Objects.requireNonNull(element, "element must not be null");
        return CLIENT.getBloomFilter(filterName).contains(element);
    }

    /**
     * 创建计数器锁存器
     *
     * @param latchName 锁存器名称
     * @param count     计数值
     * @return true：创建成功；false：创建失败
     */
    public static boolean createCountDownLatch(String latchName, int count) {
        validateKey(latchName);
        if (count <= 0) {
            throw new IllegalArgumentException("count must be positive");
        }
        return CLIENT.getCountDownLatch(latchName).trySetCount(count);
    }

    /**
     * 分布式同步工具
     */

    /**
     * 等待计数器锁存器
     *
     * @param latchName 锁存器名称
     * @param timeout   超时时间（秒）
     * @return true：成功等待；false：超时
     */
    public static boolean awaitCountDownLatch(String latchName, int timeout) {
        validateKey(latchName);
        try {
            return CLIENT.getCountDownLatch(latchName).await(timeout, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 计数器锁存器减一
     *
     * @param latchName 锁存器名称
     */
    public static void countDownLatch(String latchName) {
        validateKey(latchName);
        CLIENT.getCountDownLatch(latchName).countDown();
    }

    /**
     * 获取信号量许可
     *
     * @param semaphoreName 信号量名称
     * @param permits       许可数量
     * @param timeout       超时时间（秒）
     * @return true：获取成功；false：获取失败
     */
    public static boolean acquireSemaphore(String semaphoreName, int permits, int timeout) {
        validateKey(semaphoreName);
        if (permits <= 0) {
            throw new IllegalArgumentException("permits must be positive");
        }
        try {
            return CLIENT.getSemaphore(semaphoreName).tryAcquire(permits, timeout, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 释放信号量许可
     *
     * @param semaphoreName 信号量名称
     * @param permits       许可数量
     */
    public static void releaseSemaphore(String semaphoreName, int permits) {
        validateKey(semaphoreName);
        if (permits <= 0) {
            throw new IllegalArgumentException("permits must be positive");
        }
        CLIENT.getSemaphore(semaphoreName).release(permits);
    }

    /**
     * 设置信号量许可总数
     *
     * @param semaphoreName 信号量名称
     * @param permits       许可总数
     * @return true：设置成功；false：设置失败
     */
    public static boolean setSemaphorePermits(String semaphoreName, int permits) {
        validateKey(semaphoreName);
        if (permits <= 0) {
            throw new IllegalArgumentException("permits must be positive");
        }
        return CLIENT.getSemaphore(semaphoreName).trySetPermits(permits);
    }

    /**
     * 添加地理位置
     *
     * @param geoName   地理位置集合名称
     * @param longitude 经度
     * @param latitude  纬度
     * @param member    成员
     * @return 添加的成员数量
     */
    public static <T> long geoAdd(String geoName, double longitude, double latitude, T member) {
        validateKey(geoName);
        Objects.requireNonNull(member, "member must not be null");
        return CLIENT.getGeo(geoName).add(longitude, latitude, member);
    }

    /**
     * 地理位置操作
     */

    /**
     * 获取地理位置坐标
     *
     * @param geoName 地理位置集合名称
     * @param member  成员
     * @return 坐标数组 [经度, 纬度]，成员不存在返回null
     */
    public static <T> double[] geoPosition(String geoName, T member) {
        validateKey(geoName);
        Objects.requireNonNull(member, "member must not be null");
        Map<Object, GeoPosition> positions = CLIENT.getGeo(geoName).pos(member);
        GeoPosition pos = positions.get(member);
        if (pos != null) {
            return new double[] {pos.getLongitude(), pos.getLatitude()};
        }
        return null;
    }

    /**
     * 计算两个成员之间的距离
     *
     * @param geoName 地理位置集合名称
     * @param member1 成员1
     * @param member2 成员2
     * @return 距离（米）
     *
     *         <pre>
     *         使用案例：
     *
     *         // 添加地理位置数据
     *         RedisUtils.geoAdd("cities", 116.3974, 39.9093, "北京"); // 北京经纬度
     *         RedisUtils.geoAdd("cities", 121.4737, 31.2304, "上海"); // 上海经纬度
     *         RedisUtils.geoAdd("cities", 113.2644, 23.1291, "广州"); // 广州经纬度
     *
     *         // 计算两个城市之间的距离
     *         Double distance = RedisUtils.geoDistance("cities", "北京", "上海");
     *         System.out.println("北京到上海的距离: " + distance + " 米");
     *
     *         // 添加门店位置
     *         RedisUtils.geoAdd("stores", 116.3974, 39.9093, "店铺A");
     *         RedisUtils.geoAdd("stores", 116.4074, 39.9193, "店铺B");
     *
     *         // 计算门店之间的距离
     *         Double storeDistance = RedisUtils.geoDistance("stores", "店铺A", "店铺B");
     *         if (storeDistance != null && storeDistance &lt; 5000) {
     *         System.out.println("两个门店距离很近: " + storeDistance + " 米");
     *         }
     *
     *         // 获取位置坐标
     *         double[] beijingPos = RedisUtils.geoPosition("cities", "北京");
     *         if (beijingPos != null) {
     *         System.out.println("北京位置: 经度=" + beijingPos[0] + ", 纬度=" + beijingPos[1]);
     *         }
     *
     *         // 用于配送距离计算
     *         String userGeoKey = "user:locations";
     *         String storeGeoKey = "store:locations";
     *
     *         // 添加用户位置
     *         RedisUtils.geoAdd(userGeoKey, 116.3974, 39.9093, "user:123");
     *         // 添加商店位置
     *         RedisUtils.geoAdd(storeGeoKey, 116.4074, 39.9193, "store:456");
     *
     *         // 在不同的地理集合中需要先获取坐标再计算
     *         double[] userPos = RedisUtils.geoPosition(userGeoKey, "user:123");
     *         double[] storePos = RedisUtils.geoPosition(storeGeoKey, "store:456");
     *
     *         应用场景：
     *         - 外卖配送距离计算
     *         - 附近门店查找
     *         - 地图导航应用
     *         - 社交距离计算
     *         - 物流配送优化
     *
     *         注意：
     *         - 距离单位为米
     *         - 如果任一成员不存在，返回null
     *         - 基于球面距离计算，适用于地球表面
     *         - 精度足够满足大部分应用场景
     *         </pre>
     */
    public static <T> Double geoDistance(String geoName, T member1, T member2) {
        validateKey(geoName);
        Objects.requireNonNull(member1, "member1 must not be null");
        Objects.requireNonNull(member2, "member2 must not be null");
        return CLIENT.getGeo(geoName).dist(member1, member2, GeoUnit.METERS);
    }

    /**
     * 获取缓存，如果不存在则加载并缓存
     *
     * @param key      键
     * @param loader   加载器
     * @param duration 过期时间
     * @return 值的Optional包装
     *
     *         <pre>
     *         使用案例：
     *
     *         // 基本用法 - 获取用户信息，不存在则从数据库加载
     *         Optional&lt;User&gt; user = RedisUtils.getOrLoad(
     *         "user:profile:" + userId,
     *         () -> userService.getUserById(userId),
     *         Duration.ofHours(1)
     *         );
     *
     *         // 获取商品信息，缓存2小时
     *         Optional&lt;Product&gt; product = RedisUtils.getOrLoad(
     *         "product:" + productId,
     *         () -> productService.getProductById(productId),
     *         Duration.ofHours(2)
     *         );
     *
     *         // 获取配置信息，缓存30分钟
     *         Optional&lt;Config&gt; config = RedisUtils.getOrLoad(
     *         "config:app",
     *         () -> configService.getAppConfig(),
     *         Duration.ofMinutes(30)
     *         );
     *
     *         // 获取统计数据，缓存5分钟
     *         Optional&lt;Statistics&gt; stats = RedisUtils.getOrLoad(
     *         "stats:daily:" + date,
     *         () -> statisticsService.getDailyStats(date),
     *         Duration.ofMinutes(5)
     *         );
     *
     *         // 复杂的加载逻辑
     *         Optional&lt;List&lt;Order&gt;&gt; orders = RedisUtils.getOrLoad(
     *         "orders:user:" + userId,
     *         () -> {
     *         // 复杂的业务逻辑
     *         List&lt;Order&gt; result = orderService.getOrdersByUserId(userId);
     *         return result.isEmpty() ? null : result;
     *         },
     *         Duration.ofMinutes(10)
     *         );
     *
     *         // 在Service层使用
     *         &#64;Service
     *         public class UserService {
     *         public User getUserProfile(Long userId) {
     *         return RedisUtils.getOrLoad(
     *         "user:profile:" + userId,
     *         () -> userRepository.findById(userId).orElse(null),
     *         Duration.ofHours(1)
     *         ).orElse(null);
     *         }
     *         }
     *
     *         功能特点：
     *         - 防缓存穿透：缓存空值，避免频繁查询数据库
     *         - 防缓存击穿：使用分布式锁，避免并发加载
     *         - 防缓存雪崩：设置合理的过期时间
     *         - 双重检查：加锁后再次检查缓存，避免重复加载
     *
     *         应用场景：
     *         - 热点数据缓存
     *         - 数据库查询缓存
     *         - 配置信息缓存
     *         - 计算结果缓存
     *         - API响应缓存
     *
     *         注意：
     *         - loader返回null时会缓存空值，防止缓存穿透
     *         - 空值缓存时间较短（5分钟），避免长期缓存无效数据
     *         - 使用分布式锁，可能会有轻微的性能影响
     *         - 适合读多写少的场景
     *         </pre>
     */
    public static <T> Optional<T> getOrLoad(String key, Supplier<T> loader, Duration duration) {
        validateKey(key);
        Objects.requireNonNull(loader, "loader must not be null");
        Objects.requireNonNull(duration, "duration must not be null");

        // 先尝试从缓存获取
        Optional<T> cached = get(key);
        if (cached.isPresent()) {
            return cached;
        }

        // 使用分布式锁防止缓存击穿
        String lockKey = formatKey("lock", key);
        boolean locked = tryLock(lockKey, 30000, 5000);
        if (!locked) {
            // 获取锁失败，再次尝试从缓存获取
            return get(key);
        }

        try {
            // 双重检查
            cached = get(key);
            if (cached.isPresent()) {
                return cached;
            }

            // 加载数据
            T value = loader.get();
            if (value != null) {
                set(key, value, duration);
                return Optional.of(value);
            } else {
                // 缓存空值防止缓存穿透
                set(key, (T)"NULL", Duration.ofMinutes(5));
                return Optional.empty();
            }
        } finally {
            unlock(lockKey);
        }
    }

    /**
     * 缓存穿透防护
     */

    /**
     * 生成分布式唯一ID
     *
     * @param keyPrefix ID前缀
     * @return 唯一ID
     */
    public static long generateUniqueId(String keyPrefix) {
        validateKey(keyPrefix);
        String key = formatKey("id", keyPrefix);
        return CLIENT.getAtomicLong(key).incrementAndGet();
    }

    /**
     * 分布式唯一ID生成
     */

    /**
     * 生成基于时间戳的唯一ID
     *
     * @param keyPrefix ID前缀
     * @return 唯一ID（时间戳 + 序列号）
     */
    public static String generateTimestampId(String keyPrefix) {
        validateKey(keyPrefix);
        String key = formatKey("seq", keyPrefix);
        long timestamp = System.currentTimeMillis();
        long sequence = CLIENT.getAtomicLong(key).incrementAndGet();
        return String.format("%d%06d", timestamp, sequence % 1000000);
    }

    /**
     * 执行Lua脚本
     *
     * @param script Lua脚本
     * @param keys   键列表
     * @param values 值列表
     * @return 脚本执行结果
     *
     *         <pre>
     *         使用案例：
     *
     *         // 原子性库存扣减
     *         String stockScript =
     *         "local stock = redis.call('GET', KEYS[1])\n" +
     *         "if stock == false then\n" +
     *         " return -1\n" +
     *         "end\n" +
     *         "stock = tonumber(stock)\n" +
     *         "local quantity = tonumber(ARGV[1])\n" +
     *         "if stock >= quantity then\n" +
     *         " redis.call('DECRBY', KEYS[1], quantity)\n" +
     *         " return stock - quantity\n" +
     *         "else\n" +
     *         " return -2\n" +
     *         "end";
     *
     *         List&lt;Object&gt; keys = Arrays.asList("product:stock:123");
     *         Long result = RedisUtils.executeScript(stockScript, keys, 5);
     *         if (result == -1) {
     *         System.out.println("商品不存在");
     *         } else if (result == -2) {
     *         System.out.println("库存不足");
     *         } else {
     *         System.out.println("扣减成功，剩余库存: " + result);
     *         }
     *
     *         // 分布式限流
     *         String rateLimitScript =
     *         "local key = KEYS[1]\n" +
     *         "local limit = tonumber(ARGV[1])\n" +
     *         "local window = tonumber(ARGV[2])\n" +
     *         "local current = redis.call('INCR', key)\n" +
     *         "if current == 1 then\n" +
     *         " redis.call('EXPIRE', key, window)\n" +
     *         "end\n" +
     *         "return current";
     *
     *         List&lt;Object&gt; rateLimitKeys = Arrays.asList("rate:limit:" + userId);
     *         Long currentCount = RedisUtils.executeScript(rateLimitScript, rateLimitKeys, 10, 60);
     *         if (currentCount > 10) {
     *         throw new BusinessException("请求过于频繁");
     *         }
     *
     *         // 批量操作
     *         String batchScript =
     *         "for i = 1, #KEYS do\n" +
     *         " redis.call('SET', KEYS[i], ARGV[i])\n" +
     *         "end\n" +
     *         "return #KEYS";
     *
     *         List&lt;Object&gt; batchKeys = Arrays.asList("key1", "key2", "key3");
     *         Long count = RedisUtils.executeScript(batchScript, batchKeys, "value1", "value2", "value3");
     *         System.out.println("批量设置了 " + count + " 个键");
     *
     *         // 分布式锁续期
     *         String renewLockScript =
     *         "if redis.call('GET', KEYS[1]) == ARGV[1] then\n" +
     *         " return redis.call('EXPIRE', KEYS[1], ARGV[2])\n" +
     *         "else\n" +
     *         " return 0\n" +
     *         "end";
     *
     *         List&lt;Object&gt; lockKeys = Arrays.asList("lock:order:" + orderId);
     *         Long renewed = RedisUtils.executeScript(renewLockScript, lockKeys, lockValue, 30);
     *         if (renewed == 1) {
     *         System.out.println("锁续期成功");
     *         }
     *
     *         // 条件更新
     *         String conditionalUpdateScript =
     *         "local current = redis.call('GET', KEYS[1])\n" +
     *         "if current == ARGV[1] then\n" +
     *         " redis.call('SET', KEYS[1], ARGV[2])\n" +
     *         " return 1\n" +
     *         "else\n" +
     *         " return 0\n" +
     *         "end";
     *
     *         List&lt;Object&gt; updateKeys = Arrays.asList("user:status:" + userId);
     *         Long updated = RedisUtils.executeScript(conditionalUpdateScript, updateKeys, "online", "offline");
     *
     *         应用场景：
     *         - 原子性操作
     *         - 分布式锁
     *         - 限流控制
     *         - 库存扣减
     *         - 批量操作
     *         - 复杂业务逻辑
     *
     *         注意：
     *         - Lua脚本在Redis服务器端执行，保证原子性
     *         - 脚本中的所有操作要么全部成功，要么全部失败
     *         - 避免在脚本中使用耗时操作
     *         - KEYS和ARGV数组在Lua中从1开始索引
     *         - 脚本执行期间Redis是阻塞的，避免复杂逻辑
     *         </pre>
     */
    public static <T> T executeScript(String script, List<Object> keys, Object... values) {
        Objects.requireNonNull(script, "script must not be null");
        return CLIENT.getScript().eval(RScript.Mode.READ_WRITE, script, RScript.ReturnType.VALUE, keys, values);
    }

    /**
     * 脚本执行
     */

    /**
     * 添加延迟任务
     *
     * @param queueName 队列名称
     * @param task      任务内容
     * @param delay     延迟时间
     * @return true：添加成功；false：添加失败
     */
    public static <T> boolean addDelayedTask(String queueName, T task, Duration delay) {
        validateKey(queueName);
        Objects.requireNonNull(task, "task must not be null");
        Objects.requireNonNull(delay, "delay must not be null");

        CLIENT.getDelayedQueue(CLIENT.getQueue(queueName)).offer(task, delay.toMillis(), TimeUnit.MILLISECONDS);
        return true;
    }

    /**
     * 延迟队列操作
     */

    /**
     * 获取延迟队列大小
     *
     * @param queueName 队列名称
     * @return 队列大小
     */
    public static int getDelayedQueueSize(String queueName) {
        validateKey(queueName);
        return CLIENT.getDelayedQueue(CLIENT.getQueue(queueName)).size();
    }

    /**
     * 设置多级缓存
     *
     * @param key     键
     * @param value   值
     * @param l1Cache 一级缓存时间（本地缓存）
     * @param l2Cache 二级缓存时间（Redis缓存）
     */
    public static <T> void setMultiLevelCache(String key, T value, Duration l1Cache, Duration l2Cache) {
        validateKey(key);
        Objects.requireNonNull(value, "value must not be null");
        Objects.requireNonNull(l1Cache, "l1Cache must not be null");
        Objects.requireNonNull(l2Cache, "l2Cache must not be null");

        // 设置Redis缓存
        set(key, value, l2Cache);

        // 这里可以集成本地缓存，比如Caffeine
        // 暂时只实现Redis部分
    }

    /**
     * 多级缓存操作
     */

    /**
     * 批量预热缓存
     *
     * @param keyValueMap 键值映射
     * @param duration    过期时间
     */
    public static <T> void warmupCache(Map<String, T> keyValueMap, Duration duration) {
        Objects.requireNonNull(keyValueMap, "keyValueMap must not be null");
        Objects.requireNonNull(duration, "duration must not be null");

        if (keyValueMap.isEmpty()) {
            return;
        }

        // 使用批量操作提高性能
        setBatch(keyValueMap, duration);

        log.info("Cache warmup completed, {} keys loaded", keyValueMap.size());
    }

    /**
     * 缓存预热
     */

    /**
     * 检查Redis连接状态
     *
     * @return true：连接正常；false：连接异常
     */
    public static boolean isConnected() {
        try {
            // 执行简单的ping命令
            return CLIENT.getNodesGroup().pingAll();
        } catch (Exception e) {
            log.error("Redis health check failed", e);
            return false;
        }
    }

    /**
     * 缓存健康检查
     */

    /**
     * 获取Redis信息
     *
     * @return Redis信息映射
     */
    public static Map<String, String> getRedisInfo() {
        try {
            return CLIENT.getNodesGroup().getNodes().iterator().next().info(org.redisson.api.Node.InfoSection.ALL);
        } catch (Exception e) {
            log.error("Failed to get Redis info", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 清理过期缓存
     */
    public static void cleanExpiredCache() {
        try {
            CLIENT.getKeys().flushdb();
            log.info("Expired cache cleaned successfully");
        } catch (Exception e) {
            log.error("Failed to clean expired cache", e);
        }
    }

    // ======================== 数据结构直接获取方法 ========================

    /**
     * 获取 RSet 对象
     *
     * @param key 键
     * @return RSet对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取RSet对象进行复杂操作
     *                                  RSet&lt;String&gt; userTags = RedisUtils.getRSet("user:tags:" + userId);
     *
     *                                  // 批量添加标签
     *                                  userTags.addAll(Arrays.asList("技术", "编程", "Java"));
     *
     *                                  // 设置过期时间
     *                                  userTags.expire(Duration.ofDays(30));
     *
     *                                  // 检查标签是否存在
     *                                  if (userTags.contains("Java")) {
     *                                  System.out.println("用户对Java感兴趣");
     *                                  }
     *
     *                                  // 获取所有标签
     *                                  Set&lt;String&gt; allTags = userTags.readAll();
     *
     *                                  // 与其他集合求交集
     *                                  RSet&lt;String&gt; otherTags = RedisUtils.getRSet("popular:tags");
     *                                  Set&lt;String&gt; intersection = userTags.readIntersection(otherTags.getName());
     *
     *                                  // 异步操作
     *                                  RFuture&lt;Boolean&gt; future = userTags.addAsync("新标签");
     *
     *                                  应用场景：
     *                                  - 用户标签管理
     *                                  - 权限集合操作
     *                                  - 分类标记
     *                                  - 集合运算
     *                                  - 去重处理
     *
     *                                  注意：
     *                                  - 返回的是Redisson原生RSet对象
     *                                  - 支持所有RSet的高级操作
     *                                  - 可以进行集合间的交集、并集、差集运算
     *                                  - 支持异步操作提升性能
     *                                  </pre>
     */
    public static <T> RSet<T> getRSet(String key) {
        validateKey(key);
        return CLIENT.getSet(key);
    }

    /**
     * 获取 RList 对象
     *
     * @param key 键
     * @return RList对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取RList对象进行复杂操作
     *                                  RList&lt;String&gt; messageList = RedisUtils.getRList("messages:" + channelId);
     *
     *                                  // 添加消息到列表末尾
     *                                  messageList.add("新消息内容");
     *
     *                                  // 插入消息到指定位置
     *                                  messageList.add(0, "置顶消息");
     *
     *                                  // 获取最新的10条消息
     *                                  List&lt;String&gt; recentMessages = messageList.range(-10, -1);
     *
     *                                  // 设置列表大小限制，保留最新的100条
     *                                  messageList.trim(0, 99);
     *
     *                                  // 排序操作
     *                                  messageList.sort(Comparator.naturalOrder());
     *
     *                                  // 异步批量添加
     *                                  RFuture&lt;Boolean&gt; future = messageList.addAllAsync(Arrays.asList("msg1", "msg2"));
     *
     *                                  应用场景：
     *                                  - 消息列表管理
     *                                  - 操作历史记录
     *                                  - 排行榜数据
     *                                  - 队列处理
     *                                  - 时间线数据
     *
     *                                  注意：
     *                                  - 支持索引访问和范围查询
     *                                  - 可以进行排序和去重操作
     *                                  - 支持阻塞弹出操作
     *                                  - 内存占用随数据量增长
     *                                  </pre>
     */
    public static <T> RList<T> getRList(String key) {
        validateKey(key);
        return CLIENT.getList(key);
    }

    /**
     * 获取 RMap 对象
     *
     * @param key 键
     * @return RMap对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取RMap对象进行复杂操作
     *                                  RMap&lt;String, Object&gt; userProfile = RedisUtils.getRMap("profile:" + userId);
     *
     *                                  // 批量设置用户信息
     *                                  Map&lt;String, Object&gt; profileData = new HashMap&lt;&gt;();
     *                                  profileData.put("name", "张三");
     *                                  profileData.put("age", 25);
     *                                  profileData.put("city", "北京");
     *                                  userProfile.putAll(profileData);
     *
     *                                  // 原子性增加积分
     *                                  userProfile.addAndGet("score", 10);
     *
     *                                  // 获取指定字段
     *                                  String userName = (String) userProfile.get("name");
     *
     *                                  // 检查字段是否存在
     *                                  if (userProfile.containsKey("vip")) {
     *                                  System.out.println("用户是VIP");
     *                                  }
     *
     *                                  // 设置字段过期时间
     *                                  userProfile.expire("temp_token", Duration.ofMinutes(30));
     *
     *                                  // 获取所有字段和值
     *                                  Map&lt;String, Object&gt; allData = userProfile.readAllMap();
     *
     *                                  应用场景：
     *                                  - 用户会话存储
     *                                  - 对象属性缓存
     *                                  - 配置信息管理
     *                                  - 计数器集合
     *                                  - 临时数据存储
     *
     *                                  注意：
     *                                  - 支持原子性的数值操作
     *                                  - 可以设置单个字段的过期时间
     *                                  - 适合存储结构化数据
     *                                  - 内存使用相对高效
     *                                  </pre>
     */
    public static <K, V> RMap<K, V> getRMap(String key) {
        validateKey(key);
        return CLIENT.getMap(key);
    }

    /**
     * 获取 RScoredSortedSet 对象
     *
     * @param key 键
     * @return RScoredSortedSet对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取有序集合对象
     *                                  RScoredSortedSet&lt;String&gt; leaderboard = RedisUtils.getRScoredSortedSet("game:leaderboard");
     *
     *                                  // 添加玩家分数
     *                                  leaderboard.add(1000, "player1");
     *                                  leaderboard.add(1200, "player2");
     *                                  leaderboard.add(800, "player3");
     *
     *                                  // 增加玩家分数
     *                                  leaderboard.addScore("player1", 50);
     *
     *                                  // 获取排行榜前10名
     *                                  Collection&lt;String&gt; top10 = leaderboard.valueRangeReversed(0, 9);
     *
     *                                  // 获取玩家排名（从高分到低分）
     *                                  Integer rank = leaderboard.revRank("player1");
     *
     *                                  // 获取分数范围内的玩家
     *                                  Collection&lt;String&gt; highScorers = leaderboard.valueRange(1000, true, 2000, true);
     *
     *                                  // 删除低分玩家
     *                                  leaderboard.removeRangeByScore(0, true, 500, true);
     *
     *                                  // 获取玩家分数
     *                                  Double playerScore = leaderboard.getScore("player1");
     *
     *                                  应用场景：
     *                                  - 游戏排行榜
     *                                  - 热点数据排序
     *                                  - 优先级队列
     *                                  - 时间范围查询
     *                                  - 分数统计
     *
     *                                  注意：
     *                                  - 自动按分数排序
     *                                  - 支持范围查询和排名查询
     *                                  - 适合需要排序的数据集
     *                                  - 分数可以是小数
     *                                  </pre>
     */
    public static <T> RScoredSortedSet<T> getRScoredSortedSet(String key) {
        validateKey(key);
        return CLIENT.getScoredSortedSet(key);
    }

    /**
     * 获取 RQueue 对象
     *
     * @param key 键
     * @return RQueue对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取队列对象
     *                                  RQueue&lt;String&gt; taskQueue = RedisUtils.getRQueue("tasks:email");
     *
     *                                  // 添加任务到队列
     *                                  taskQueue.offer("发送欢迎邮件给用户A");
     *                                  taskQueue.offer("发送订单确认邮件给用户B");
     *
     *                                  // 获取队列大小
     *                                  int queueSize = taskQueue.size();
     *
     *                                  // 查看队列头部元素（不移除）
     *                                  String nextTask = taskQueue.peek();
     *
     *                                  // 移除并获取队列头部元素
     *                                  String task = taskQueue.poll();
     *                                  if (task != null) {
     *                                  processEmailTask(task);
     *                                  }
     *
     *                                  // 批量处理队列中的任务
     *                                  while (!taskQueue.isEmpty()) {
     *                                  String currentTask = taskQueue.poll();
     *                                  if (currentTask != null) {
     *                                  processTask(currentTask);
     *                                  }
     *                                  }
     *
     *                                  应用场景：
     *                                  - 任务队列处理
     *                                  - 消息缓冲
     *                                  - 异步处理
     *                                  - 生产者消费者模式
     *                                  - 订单处理队列
     *
     *                                  注意：
     *                                  - FIFO（先进先出）顺序
     *                                  - 非阻塞操作
     *                                  - 适合高并发场景
     *                                  - 需要手动处理空队列情况
     *                                  </pre>
     */
    public static <T> RQueue<T> getRQueue(String key) {
        validateKey(key);
        return CLIENT.getQueue(key);
    }

    /**
     * 获取 RBlockingQueue 对象
     *
     * @param key 键
     * @return RBlockingQueue对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取阻塞队列对象
     *                                  RBlockingQueue&lt;String&gt; blockingQueue = RedisUtils.getRBlockingQueue("blocking:tasks");
     *
     *                                  // 生产者：添加任务
     *                                  blockingQueue.put("重要任务1");
     *                                  blockingQueue.offer("普通任务2", 10, TimeUnit.SECONDS);
     *
     *                                  // 消费者：阻塞等待任务
     *                                  try {
     *                                  String task = blockingQueue.take(); // 无限等待
     *                                  processTask(task);
     *                                  } catch (InterruptedException e) {
     *                                  Thread.currentThread().interrupt();
     *                                  }
     *
     *                                  // 带超时的阻塞等待
     *                                  try {
     *                                  String task = blockingQueue.poll(30, TimeUnit.SECONDS);
     *                                  if (task != null) {
     *                                  processTask(task);
     *                                  } else {
     *                                  System.out.println("30秒内没有任务");
     *                                  }
     *                                  } catch (InterruptedException e) {
     *                                  Thread.currentThread().interrupt();
     *                                  }
     *
     *                                  // 批量消费
     *                                  List&lt;String&gt; tasks = new ArrayList&lt;&gt;();
     *                                  blockingQueue.drainTo(tasks, 10); // 最多取10个任务
     *
     *                                  应用场景：
     *                                  - 工作线程池
     *                                  - 消息消费
     *                                  - 任务调度
     *                                  - 生产者消费者模式
     *                                  - 流量控制
     *
     *                                  注意：
     *                                  - 阻塞操作会等待直到有元素或超时
     *                                  - 适合多线程环境
     *                                  - 可能抛出InterruptedException
     *                                  - 支持批量操作提高效率
     *                                  </pre>
     */
    public static <T> RBlockingQueue<T> getRBlockingQueue(String key) {
        validateKey(key);
        return CLIENT.getBlockingQueue(key);
    }

    /**
     * 获取 RAtomicLong 对象
     *
     * @param key 键
     * @return RAtomicLong对象
     * @throws IllegalArgumentException 如果key为null
     *
     *                                  <pre>
     *                                  使用案例：
     *
     *                                  // 获取原子长整型对象
     *                                  RAtomicLong counter = RedisUtils.getRAtomicLong("global:counter");
     *
     *                                  // 原子性递增
     *                                  long newValue = counter.incrementAndGet();
     *                                  System.out.println("当前计数: " + newValue);
     *
     *                                  // 原子性递减
     *                                  long decrementValue = counter.decrementAndGet();
     *
     *                                  // 原子性加法
     *                                  long addResult = counter.addAndGet(10);
     *
     *                                  // 比较并设置
     *                                  boolean updated = counter.compareAndSet(100, 200);
     *                                  if (updated) {
     *                                  System.out.println("成功将100更新为200");
     *                                  }
     *
     *                                  // 获取当前值
     *                                  long currentValue = counter.get();
     *
     *                                  // 设置值
     *                                  counter.set(1000);
     *
     *                                  // 获取并设置新值
     *                                  long oldValue = counter.getAndSet(2000);
     *
     *                                  应用场景：
     *                                  - 全局计数器
     *                                  - 序列号生成
     *                                  - 统计数据
     *                                  - 限流计数
     *                                  - 业务流水号
     *
     *                                  注意：
     *                                  - 所有操作都是原子性的
     *                                  - 适合高并发计数场景
     *                                  - 支持比较并交换操作
     *                                  - 内存占用极小
     *                                  </pre>
     */
    public static RAtomicLong getRAtomicLong(String key) {
        validateKey(key);
        return CLIENT.getAtomicLong(key);
    }

    /**
     * 操作统计信息
     */
    @Data
    @AllArgsConstructor
    public static class OperationStats {

        private final long operationCount;
        private final long errorCount;
        private final double averageTime;
    }
}
