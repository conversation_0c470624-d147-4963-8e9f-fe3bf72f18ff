<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzProductSkuMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzProductSku">
    <!--@mbg.generated-->
    <!--@Table tz_product_sku-->
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="spu_id" jdbcType="BIGINT" property="spuId"/>
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode"/>
    <result column="platform_product_id" jdbcType="VARCHAR" property="platformProductId"/>
    <result column="platform_sku" jdbcType="VARCHAR" property="platformSku"/>
    <result column="platform_spec_id" jdbcType="VARCHAR" property="platformSpecId"/>
    <result column="sku" jdbcType="VARCHAR" property="sku"/>
    <result column="barcode" jdbcType="VARCHAR" property="barcode"/>
    <result column="image" jdbcType="VARCHAR" property="image"/>
    <result column="min_order_quantity" jdbcType="INTEGER" property="minOrderQuantity"/>
    <result column="quantity" jdbcType="INTEGER" property="quantity"/>
    <result column="price" jdbcType="DECIMAL" property="price"/>
    <result column="drop_shipping_price" jdbcType="DECIMAL" property="dropShippingPrice"/>
    <result column="sales_info" jdbcType="VARCHAR" property="salesInfo" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    <result column="specs" jdbcType="VARCHAR" property="specs" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    <result column="sales_count" jdbcType="INTEGER" property="salesCount"/>
    <result column="unit_info" jdbcType="VARCHAR" property="unitInfo" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted"/>
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    <result column="revision" jdbcType="INTEGER" property="revision"/>
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated"/>
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, spu_id, platform_code, platform_product_id, platform_sku, platform_spec_id, sku,
    barcode, image,min_order_quantity, quantity, price, drop_shipping_price, sales_info, specs, sales_count,
    unit_info, is_deleted, tenant_id, revision, gmt_created, gmt_modified
  </sql>

  <insert id="batchInsertSkus">
    insert into tz_product_sku (id, spu_id, platform_code, platform_product_id, platform_sku, platform_spec_id, sku,
    barcode, image, min_order_quantity, quantity, price, drop_shipping_price, sales_info, specs, sales_count,
    unit_info)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.spuId}, #{item.platformCode}, #{item.platformProductId}, #{item.platformSku}, #{item.platformSpecId}, #{item.sku},
      #{item.barcode}, #{item.image}, #{item.minOrderQuantity}, #{item.quantity}, #{item.price}, #{item.dropShippingPrice},
      #{item.salesInfo,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
      #{item.specs,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}, #{item.salesCount},
      #{item.unitInfo,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
    </foreach>
  </insert>
</mapper>
