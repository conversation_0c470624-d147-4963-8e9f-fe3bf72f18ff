<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TzOrderSupplier">
    <!--@mbg.generated-->
    <!--@Table tz_order_supplier-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_id" jdbcType="BIGINT" property="purchaseOrderId" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
    <result column="platform_code" jdbcType="VARCHAR" property="platformCode" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_shop_name" jdbcType="VARCHAR" property="supplierShopName" />
    <result column="metadata_json" jdbcType="VARCHAR" property="metadataJson" />
    <result column="is_multiple_orders" jdbcType="TINYINT" property="isMultipleOrders" />
    <result column="external_platform_order_id" jdbcType="VARCHAR" property="externalPlatformOrderId" />
    <result column="external_platform_order_ids" jdbcType="VARCHAR" property="externalPlatformOrderIds" />
    <result column="external_platform_order_no" jdbcType="VARCHAR" property="externalPlatformOrderNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="order_date" jdbcType="TIMESTAMP" property="orderDate" />
    <result column="payment_date" jdbcType="TIMESTAMP" property="paymentDate" />
    <result column="procurement_date" jdbcType="TIMESTAMP" property="procurementDate" />
    <result column="shipped_date" jdbcType="TIMESTAMP" property="shippedDate" />
    <result column="delivered_date" jdbcType="TIMESTAMP" property="deliveredDate" />
    <result column="completed_date" jdbcType="TIMESTAMP" property="completedDate" />
    <result column="cancelled_date" jdbcType="TIMESTAMP" property="cancelledDate" />
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount" />
    <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="payable_amount" jdbcType="DECIMAL" property="payableAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="real_pay_amount" jdbcType="DECIMAL" property="realPayAmount" />
    <result column="line_item_count" jdbcType="INTEGER" property="lineItemCount" />
    <result column="supplier_notes" jdbcType="LONGVARCHAR" property="supplierNotes" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, purchase_order_id, supplier_order_no, platform_code, supplier_id, supplier_name,
    supplier_shop_name, metadata_json, is_multiple_orders, external_platform_order_id,
    external_platform_order_ids, external_platform_order_no, `status`, order_date, payment_date,
    procurement_date, shipped_date, delivered_date, completed_date, cancelled_date, goods_amount,
    freight_amount, tax_amount, service_fee, payable_amount, discount_amount, real_pay_amount,
    line_item_count, supplier_notes, tenant_id, is_deleted, revision, gmt_created, gmt_modified
  </sql>

  <insert id="insertBatch">
    insert into tz_order_supplier (id, purchase_order_id, supplier_order_no, platform_code, supplier_id, supplier_name,
    supplier_shop_name, metadata_json, is_multiple_orders, external_platform_order_id,
    external_platform_order_ids, external_platform_order_no, `status`, order_date, payment_date,
    procurement_date, shipped_date, delivered_date, completed_date, cancelled_date, goods_amount,
    freight_amount, tax_amount, service_fee, payable_amount, discount_amount, real_pay_amount,
    line_item_count, supplier_notes, tenant_id
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.purchaseOrderId}, #{item.supplierOrderNo}, #{item.platformCode}, #{item.supplierId}, #{item.supplierName},
      #{item.supplierShopName}, #{item.metadataJson}, #{item.isMultipleOrders}, #{item.externalPlatformOrderId},
      #{item.externalPlatformOrderIds}, #{item.externalPlatformOrderNo}, #{item.status}, #{item.orderDate}, #{item.paymentDate},
      #{item.procurementDate}, #{item.shippedDate}, #{item.deliveredDate}, #{item.completedDate}, #{item.cancelledDate}, #{item.goodsAmount},
      #{item.freightAmount}, #{item.taxAmount}, #{item.serviceFee}, #{item.payableAmount}, #{item.discountAmount}, #{item.realPayAmount},
      #{item.lineItemCount}, #{item.supplierNotes}, #{item.tenantId}
      )
    </foreach>
  </insert>
</mapper>
