/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.dao.mapper;

import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.starter.data.mp.base.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/6/24 22:01
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface TzProductSkuMapper extends BaseMapper<TzProductSku> {

    /**
     * 批量插入SKU
     *
     * @param skus SKU列表
     * @return 是否成功
     */
    boolean batchInsertSkus(List<TzProductSku> skus);
}
