/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.config.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.fulfillmen.starter.web.util.ServletUtils;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.AntPathMatcher;

/**
 * 全局 MDC 过滤器
 * <p>
 * 为所有请求设置 MDC 上下文信息，包括：
 * <ul>
 * <li>用户信息（如果已登录）</li>
 * <li>请求基本信息（IP、浏览器、操作系统等）</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/5 12:49
 * @description: 全局 MDC 过滤器，统一处理请求上下文信息
 * @since 1.0.0
 */
@Slf4j
public class GlobalMDCFilter implements Filter {

    /**
     * 默认忽略 MDC 处理的URL模式 对于这些请求，不设置 MDC 信息以提高性能
     */
    private static final Set<String> DEFAULT_IGNORED_URL_PATTERNS = new HashSet<>(Arrays.asList(
        "/error", "/error/**",
        "/health", "/health/**",
        "/actuator/**",
        "/alibaba/callback/**",
        "/swagger-ui/**",
        "/v3/api-docs/**",
        "/webjars/**",
        "/favicon.ico",
        "/static/**",
        "/public/**",
        "/assets/**",
        "/*.js", "/*.css", "/*.png", "/*.jpg", "/*.jpeg", "/*.gif", "/*.ico", "/*.svg"
    ));
    /**
     * 路径匹配器
     */
    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();

    /**
     * 过滤器配置（可选，如果没有配置则使用默认值）
     */
    private FilterConfigurationProperties.MdcFilterConfig config;

    /**
     * 构造函数
     */
    public GlobalMDCFilter() {
        // 默认构造函数，使用默认配置
    }

    /**
     * 构造函数（带配置）
     *
     * @param config MDC 过滤器配置
     */
    public GlobalMDCFilter(FilterConfigurationProperties.MdcFilterConfig config) {
        this.config = config;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        if (!(request instanceof HttpServletRequest httpRequest)) {
            chain.doFilter(request, response);
            return;
        }

        // 检查是否需要忽略 MDC 处理
        if (shouldIgnoreRequest(httpRequest)) {
            log.debug("忽略 MDC 处理的请求: {}", httpRequest.getRequestURI());
            chain.doFilter(request, response);
            return;
        }

        try {
            // 设置基本请求信息到 MDC
            setBasicRequestInfoToMDC(httpRequest);

            // 继续执行过滤器链
            chain.doFilter(request, response);

        } finally {
            // 清理 MDC 信息，避免内存泄漏
            clearMDC();
        }
    }

    /**
     * 判断是否应该忽略当前请求的 MDC 处理
     *
     * @param request HTTP 请求
     * @return true 如果应该忽略，false 否则
     */
    private boolean shouldIgnoreRequest(HttpServletRequest request) {
        String requestURI = request.getRequestURI();

        // 获取忽略模式（优先使用配置，否则使用默认值）
        Set<String> ignoredPatterns = getIgnoredPatterns();

        // 检查是否匹配忽略的URL模式
        return ignoredPatterns.stream().anyMatch(pattern -> PATH_MATCHER.match(pattern, requestURI));

    }

    /**
     * 获取忽略的 URL 模式
     *
     * @return 忽略的 URL 模式集合
     */
    private Set<String> getIgnoredPatterns() {
        if (config != null && !config.getExcludePatterns().isEmpty()) {
            return new HashSet<>(config.getExcludePatterns());
        }
        return DEFAULT_IGNORED_URL_PATTERNS;
    }

    /**
     * 设置基本请求信息到 MDC
     *
     * @param request HTTP 请求
     */
    private void setBasicRequestInfoToMDC(HttpServletRequest request) {
        try {
            // 获取客户端 IP
            String clientIp = JakartaServletUtil.getClientIP(request);
            if (StrUtil.isNotBlank(clientIp)) {
                // 根据配置决定是否标准化 IP 地址
                String displayIp = shouldNormalizeIpAddress() ? normalizeIpAddress(clientIp) : clientIp;
                MDC.put(MDC_KEYS.IP, displayIp);
            }

            // 根据配置决定是否获取浏览器信息
            if (shouldIncludeBrowserInfo()) {
                String browser = ServletUtils.getBrowser(request);
                if (StrUtil.isNotBlank(browser)) {
                    MDC.put(MDC_KEYS.BROWSER, browser);
                }
            }

            // 根据配置决定是否获取操作系统信息
            if (shouldIncludeOsInfo()) {
                String os = ServletUtils.getOs(request);
                if (StrUtil.isNotBlank(os)) {
                    // 处理操作系统信息，去掉 " or" 后面的内容
                    String cleanOs = StrUtil.subBefore(os, " or", false);
                    MDC.put(MDC_KEYS.OS, cleanOs);
                }
            }

        } catch (Exception e) {
            log.warn("设置基本请求信息到 MDC 失败", e);
        }
    }

    /**
     * 清理 MDC 信息
     */
    private void clearMDC() {
        try {
            MDC.remove(MDC_KEYS.IP);
            MDC.remove(MDC_KEYS.OS);
            MDC.remove(MDC_KEYS.BROWSER);
        } catch (Exception e) {
            log.warn("清理 MDC 信息失败", e);
        }
    }

    /**
     * 标准化 IP 地址显示 将 IPv6 本地回环地址转换为更友好的显示格式
     *
     * @param ip 原始 IP 地址
     * @return 标准化后的 IP 地址
     */
    private String normalizeIpAddress(String ip) {
        if (StrUtil.isBlank(ip)) {
            return ip;
        }

        // 处理 IPv6 本地回环地址
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return "127.0.0.1";
        }

        // 处理其他常见的本地地址
        if ("0.0.0.0".equals(ip)) {
            return "127.0.0.1";
        }

        return ip;
    }

    /**
     * 是否应该标准化 IP 地址
     */
    private boolean shouldNormalizeIpAddress() {
        return config == null || config.isNormalizeIpAddress();
    }

    /**
     * 是否应该包含浏览器信息
     */
    private boolean shouldIncludeBrowserInfo() {
        return config == null || config.isIncludeBrowserInfo();
    }

    /**
     * 是否应该包含操作系统信息
     */
    private boolean shouldIncludeOsInfo() {
        return config == null || config.isIncludeOsInfo();
    }

    /**
     * MDC 键名常量（与 logback-spring.xml 中的键名保持一致）
     */
    public static final class MDC_KEYS {

        /**
         * 用户IP地址
         */
        public static final String IP = "ip";
        /**
         * 操作系统
         */
        public static final String OS = "os";
        /**
         * 浏览器信息
         */
        public static final String BROWSER = "browser";

        private MDC_KEYS() {
        }
    }

}
