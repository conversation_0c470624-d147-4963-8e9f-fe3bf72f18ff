<template>
    <div class="auth-container">
        <!-- WMS登录状态提示 -->
        <div v-if="isWmsLogin" class="wms-login-overlay">
            <div class="wms-login-status">
                <div class="wms-login-spinner"></div>
                <p>{{ t('login.wms.processing') }}</p>
            </div>
        </div>

        <!-- 左侧Banner区域 -->
        <div class="auth-banner">
            <div class="banner-content">
                <h1 class="banner-title">{{ t('login.welcomeBack') }}</h1>
                <p class="banner-text">{{ t('login.bannerText') }}</p>
            </div>
        </div>

        <!-- 右侧登录表单 -->
        <div class="auth-form-container">
            <div class="auth-form-wrapper">
                <h3 class="auth-title">{{ t('login.title') }}</h3>

                <el-form ref="loginForm" :model="formData" :rules="rules" class="auth-form" @submit.prevent="handleLogin">
                    <!-- 邮箱输入框 -->
                    <el-form-item prop="username">
                        <el-input v-model="formData.username" :placeholder="t('login.email.placeholder')" class="auth-input">
                            <template #prefix>
                                <i class="el-icon-message">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="#8A959E"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    >
                                        <rect x="2" y="4" width="20" height="16" rx="2"></rect>
                                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                    </svg>
                                </i>
                            </template>
                        </el-input>
                    </el-form-item>

                    <!-- 密码输入框 -->
                    <el-form-item prop="password">
                        <el-input v-model="formData.password" type="password" :placeholder="t('login.password.placeholder')" show-password class="auth-input">
                            <template #prefix>
                                <i class="el-icon-lock">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="#8A959E"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    >
                                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                    </svg>
                                </i>
                            </template>
                        </el-input>
                    </el-form-item>

                    <!-- 验证码输入框 -->
                    <el-form-item prop="captcha">
                        <div class="captcha-container">
                            <el-input v-model="formData.captcha" :placeholder="t('login.captcha.placeholder')" class="captcha-input">
                                <template #prefix>
                                    <i class="el-icon-lock">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="20"
                                            height="20"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="#8A959E"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        >
                                            <path d="M2 12c0-3.5 2.5-6 6.5-6s6 2.5 6 6"></path>
                                            <path d="M14 12c0-3.5 2.5-6 6-6 3.5 0 6 2.5 6 6"></path>
                                            <path d="M2 12v2c0 3.5 2.5 6 6 6s6-2.5 6-6v-2"></path>
                                            <path d="M14 12v2c0 3.5 2.5 6 6 6s6-2.5 6-6v-2"></path>
                                        </svg>
                                    </i>
                                </template>
                            </el-input>
                            <Captcha
                                mode="backend"
                                :imgSrc="captchaText"
                                :uuid="formData.uuid"
                                :expireTime="captchaExpireTime"
                                :width="120"
                                :height="40"
                                @refresh="refreshCaptcha"
                                @update:uuid="val => (formData.uuid = val)"
                            />
                        </div>
                    </el-form-item>

                    <!-- 记住我和忘记密码 -->
                    <div class="remember-forgot">
                        <div class="remember-me">
                            <el-checkbox v-model="formData.remember" class="remember-checkbox">
                                {{ t('login.rememberMe') }}
                            </el-checkbox>
                        </div>
                        <a href="#" class="forgot-link">
                            {{ t('login.forgotPassword') }}
                        </a>
                    </div>

                    <!-- 登录按钮 -->
                    <button type="submit" class="auth-button" :disabled="loading">
                        <span v-if="!loading">{{ t('login.loginButton') }}</span>
                        <span v-else class="loading-spinner"></span>
                    </button>
                </el-form>

                <!-- 错误提示 -->
                <div v-if="loginError" class="auth-error">
                    <el-alert :title="loginError" type="error" show-icon />
                </div>

                <!-- 分隔线 -->
                <div class="divider">
                    <span>{{ t('login.orContinueWith') }}</span>
                </div>

                <!-- 社交媒体登录 -->
                <div class="social-login">
                    <button class="social-button google-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="social-icon">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                            <path
                                fill="#34A853"
                                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                            />
                            <path
                                fill="#FBBC05"
                                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                            />
                            <path
                                fill="#EA4335"
                                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                            />
                        </svg>
                    </button>
                    <button class="social-button facebook-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="social-icon">
                            <path
                                fill="#1877F2"
                                d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                            />
                        </svg>
                    </button>
                </div>

                <!-- 注册链接 -->
                <div class="register-link">
                    <span>{{ t('login.noAccount') }}</span>
                    <router-link to="/register" class="register-button" v-slot="{ navigate }" custom>
                        <transition name="fade-slide">
                            <button @click="navigate" class="register-button">
                                {{ t('login.register') }}
                            </button>
                        </transition>
                    </router-link>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { authApi } from '@/api/modules/auth'
    import { captchaApi } from '@/api/modules/captcha'
    import Captcha from '@/components/common/Captcha.vue'
    import { useUserStore } from '@/stores/user'
    import { ElMessage, FormInstance } from 'element-plus'
    import { onMounted, onUnmounted, reactive, ref } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRoute, useRouter } from 'vue-router'

    const { t } = useI18n()
    const router = useRouter()
    const route = useRoute()
    const loginForm = ref<FormInstance>()
    const loading = ref(false)
    const loginError = ref('')
    const captchaText = ref('')
    const userStore = useUserStore()
    const isWmsLogin = ref(false) // 标记是否正在进行WMS登录

    const captchaExpireTime = ref(0)
    let captchaTimer: ReturnType<typeof setInterval> | null = null

    // 登录表单数据
    const formData = reactive({
        username: '',
        password: '',
        captcha: '',
        remember: false,
        uuid: '', // 验证码标识
    })

    // 表单验证规则
    const rules = {
        username: [
            { required: true, message: t('login.email.required'), trigger: 'blur' },
            { type: 'email', message: t('login.email.invalid'), trigger: 'blur' },
        ],
        password: [
            { required: true, message: t('login.password.required'), trigger: 'blur' },
            { min: 6, message: t('login.password.length'), trigger: 'blur' },
        ],
        captcha: [{ required: true, message: t('login.captcha.required'), trigger: 'blur' }],
    }

    // WMS登录处理
    const handleWmsLogin = async (code: string) => {
        try {
            loading.value = true
            loginError.value = ''
            isWmsLogin.value = true

            // 显示处理中提示
            ElMessage({
                type: 'info',
                message: t('login.wms.processing'),
            })

            // 从路由中获取 cusCode
            const cusCode = route.query.cusCode as string;

            // 调用WMS登录API，并传入 cusCode
            const response = await authApi.loginByWms(code, cusCode)

            // 登录成功，存储token
            localStorage.setItem('token', response.tokenValue)

            // 存储用户信息到Pinia
            userStore.setUserInfo(response)

            ElMessage({
                type: 'success',
                message: t('login.wms.success'),
            })

            // 清理URL中的code参数
            const { redirect, ...otherQuery } = route.query
            const targetPath = (redirect as string) || '/'

            router.replace({
                path: targetPath,
                query: { ...otherQuery },
            })
        } catch (error: any) {
            console.error('WMS登录失败:', error)
            loginError.value = error.response?.data?.msg || t('login.wms.failed')

            ElMessage({
                type: 'error',
                message: loginError.value,
            })

            // WMS登录失败，显示普通登录界面
            generateCaptcha()
        } finally {
            loading.value = false
            isWmsLogin.value = false
        }
    }

    // 生成随机验证码
    const generateCaptcha = async () => {
        try {
            // 获取验证码
            const captchaResponse = await captchaApi.getCaptcha()
            console.log('获取验证码响应:', captchaResponse)

            // 直接使用返回的Base64图片
            captchaText.value = captchaResponse.img
            console.log('验证码图片地址:', captchaText.value ? '已获取' : '未获取')
            // 更新验证码标识
            formData.uuid = captchaResponse.uuid
            captchaExpireTime.value = captchaResponse.expireTime // 假设为毫秒
        } catch (error) {
            console.error('获取验证码失败:', error)
            ElMessage.error('获取验证码失败，请稍后重试')
        }
    }

    // 刷新验证码
    const refreshCaptcha = () => {
        generateCaptcha()
        formData.captcha = '' // 清空用户输入
    }

    // 页面加载时检查URL参数并初始化
    onMounted(() => {
        console.log('==== LoginView 组件已挂载 ====')

        // 检查是否需要记住用户名
        const savedUsername = localStorage.getItem('rememberedUser')
        if (savedUsername) {
            formData.username = savedUsername
            formData.remember = true
        }

        // 检查URL中是否存在code参数
        const code = route.query.code as string
        if (code) {
            console.log('检测到WMS授权码，正在处理WMS登录')
            handleWmsLogin(code)
        } else {
            // 普通登录流程，初始化验证码
            generateCaptcha()
            captchaTimer = setInterval(() => {
                if (Date.now() >= captchaExpireTime.value) {
                    console.log('验证码过期，刷新验证码')
                    refreshCaptcha()
                }
            }, 5000)
        }
    })

    onUnmounted(() => {
        if (captchaTimer) {
            clearInterval(captchaTimer)
            captchaTimer = null
        }
    })

    // 处理登录表单提交
    const handleLogin = async () => {
        if (!loginForm.value) return

        await loginForm.value.validate(async valid => {
            if (valid) {
                try {
                    loading.value = true
                    loginError.value = ''

                    // 调用登录API
                    const response = await authApi.login({
                        account: formData.username,
                        password: formData.password,
                        captchaCode: formData.captcha,
                        captchaId: formData.uuid,
                        rememberMe: formData.remember,
                    } as any)

                    // 根据"记住我"选项，决定token存储方式
                    if (formData.remember) {
                        localStorage.setItem('token', response.tokenValue)
                        localStorage.setItem('rememberedUser', formData.username)
                    } else {
                        sessionStorage.setItem('token', response.tokenValue)
                        localStorage.removeItem('rememberedUser')
                    }

                    // 直接存储用户信息到 Pinia
                    userStore.setUserInfo(response)

                    ElMessage({
                        type: 'success',
                        message: t('login.loginSuccess'),
                    })

                    // 跳转到首页或者来源页面
                    const redirect = (router.currentRoute.value.query.redirect as string) || '/'
                    router.push(redirect)
                } catch (error: any) {
                    console.error('Login error:', error)
                    loginError.value = error.response?.data?.msg || t('login.loginFailed')
                    refreshCaptcha() // 刷新验证码
                } finally {
                    loading.value = false
                }
            }
        })
    }
</script>

<style lang="scss" scoped>
    .auth-container {
        display: flex;
        min-height: calc(100vh - 450px);
        background-color: #ffffff;
        overflow: hidden;
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
        margin: 0 auto;
        position: relative;
    }

    // WMS登录覆盖层样式
    .wms-login-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 100;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 12px;
    }

    .wms-login-status {
        background: white;
        padding: 24px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 300px;
    }

    .wms-login-spinner {
        display: inline-block;
        width: 40px;
        height: 40px;
        margin-bottom: 16px;
        border: 3px solid rgba(82, 1, 141, 0.3);
        border-radius: 50%;
        border-top-color: #52018d;
        animation: spin 1s ease-in-out infinite;
    }

    // 左侧Banner样式
    .auth-banner {
        flex: 1;
        position: relative;
        background-color: #f8f0ff;
        display: flex;
        align-items: right;
        justify-content: right;
        min-width: 40%;
        border-radius: 12px 0 0 12px;

        .banner-content {
            display: none;
        }

        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: min(600px, 90%);
            height: min(600px, 90vh);
            background-image: url('@/assets/auth/shopping.png');
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 1;
        }

        @media (max-width: 1023px) {
            display: none;
        }
    }

    // 右侧登录表单样式
    .auth-form-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0;
        background-color: #f8f0ff;
        max-width: 60%;
        border-radius: 0 12px 12px 0;
    }

    .auth-form-wrapper {
        width: 100%;
        max-width: 570px;
        background-color: white;
        padding: 24px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    }

    .auth-title {
        font-size: 28px;
        font-weight: 700;
        color: #000;
        margin-bottom: 8px;
        text-align: center;
        letter-spacing: -0.5px;
    }

    .auth-subtitle {
        font-size: 16px;
        color: #606060;
        margin-bottom: 16px;
        text-align: center;
    }

    .auth-form {
        margin-bottom: 12px;
    }

    .auth-input {
        margin-bottom: 12px;

        :deep(.el-input__wrapper) {
            height: 44px;
            padding: 0 12px;
        }
    }

    .captcha-container {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;

        .captcha-input {
            flex: 1;

            :deep(.el-input__wrapper) {
                height: 44px;
            }
        }

        .captcha-box {
            width: 120px;
            height: 44px;
            border-radius: 8px;
            background: #f0f2f5;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            overflow: hidden;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }

    .remember-forgot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .remember-me {
            .remember-checkbox {
                font-size: 14px;
                color: #606060;
            }
        }

        .forgot-link {
            font-size: 14px;
            color: #52018d;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .auth-button {
        width: 100%;
        height: 44px;
        background: #52018d;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        margin-bottom: 12px;

        &:hover {
            background: #733ca8;
        }

        &:disabled {
            background: #9c9c9c;
            cursor: not-allowed;
        }
    }

    .social-login {
        display: flex;
        gap: 8px;
        margin-bottom: 12px;
    }

    .social-button {
        flex: 1;
        height: 44px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }
    }

    .divider {
        display: flex;
        align-items: center;
        margin: 12px 0;

        &::before,
        &::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #e5e7eb;
        }

        span {
            padding: 0 12px;
            color: #6b7280;
            font-size: 14px;
        }
    }

    .register-link {
        text-align: center;
        font-size: 14px;
        color: #6b7280;
        margin-top: 12px;
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin: 0 auto;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .auth-error {
        margin: 1rem 0;

        :deep(.el-alert) {
            border-radius: 8px;

            .el-alert__title {
                font-size: 14px;
                line-height: 1.5;
            }
        }
    }

    .register-button {
        color: #52018d;
        text-decoration: none;
        font-weight: 600;
        margin-left: 0.5rem;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        font-size: inherit;

        &:hover {
            text-decoration: underline;
        }
    }

    .social-icon {
        width: 24px;
        height: 24px;
    }

    /* 过渡动画效果 */
    .fade-slide-enter-active,
    .fade-slide-leave-active {
        transition: all 0.3s ease;
    }

    .fade-slide-enter-from,
    .fade-slide-leave-to {
        opacity: 0;
        transform: translateX(10px);
    }

    // 响应式调整
    @media (max-width: 1023px) {
        .auth-container {
            flex-direction: column;
            min-height: calc(100vh - 80px);
        }

        .auth-form-container {
            max-width: 100%;
            padding: 16px;
        }
    }

    @media (max-width: 640px) {
        .auth-container {
            min-height: calc(100vh - 60px);
            margin: 0;
            border-radius: 0;
            box-shadow: none;
        }

        .auth-form-wrapper {
            padding: 16px;
        }

        .social-login {
            flex-direction: column;
        }
    }
</style>
