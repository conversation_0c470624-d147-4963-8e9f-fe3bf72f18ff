<template>
    <div class="bg-gray-50 py-8 min-h-screen">
        <div class="w-full max-w-[85%] mx-auto px-4 lg:px-6 xl:px-8">
            <!-- 步骤指示器 -->
            <CheckoutStepIndicator :current-step="3" />

            <div class="success-content">
                <!-- 成功消息 -->
                <div class="success-message-card">
                    <div class="success-icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="64"
                            height="64"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        >
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                        </svg>
                    </div>
                    <h1 class="success-title">{{ t('orderSuccess.thankYou') }}</h1>
                    <p class="success-description">{{ t('orderSuccess.orderConfirmed') }}</p>
                    <div class="order-number">
                        <span class="order-number-label">{{ t('orderSuccess.orderNumber') }}:</span>
                        <span class="order-number-value">{{ orderData?.purchaseOrderNo || orderNumber }}</span>
                    </div>
                </div>

                <!-- 订单详情 -->
                <div class="order-details-card" v-if="orderData">
                    <h2 class="details-title">{{ t('orderSuccess.orderDetails') }}</h2>

                    <div class="order-info">
                        <div class="info-section">
                            <h3 class="section-title">{{ t('orderSuccess.orderInfo') }}</h3>
                            <div class="info-content">
                                <div class="info-row">
                                    <span class="info-label">{{ t('orderSuccess.orderDate') }}:</span>
                                    <span class="info-value">{{ formatDate(orderData.submitSummary?.submitTime) }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">{{ t('orderSuccess.orderSummary.productTypes') }}:</span>
                                    <span class="info-value">{{ orderData.submitSummary?.productTypeCount || 0 }} {{ t('common.types') }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">{{ t('orderSuccess.orderSummary.totalQuantity') }}:</span>
                                    <span class="info-value">{{ orderData.submitSummary?.totalQuantity || 0 }} {{ t('common.pieces') }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-section">
                            <h3 class="section-title">{{ t('orderSuccess.deliveryInfo') }}</h3>
                            <div class="info-content">
                                <p class="delivery-line">{{ t('orderSuccess.deliveryToWarehouse') }}</p>
                                <p class="delivery-line">{{ t('orderSuccess.consolidatedShipping') }}</p>
                                <p class="delivery-line">{{ t('orderSuccess.trackingAvailable') }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- 价格详情 -->
                    <div class="price-summary" v-if="orderData.priceDetails">
                        <h3 class="section-title">{{ t('orderSuccess.priceBreakdown') }}</h3>

                        <div class="price-details">
                            <!-- 商品金额 -->
                            <div class="price-row">
                                <span class="price-label">{{ t('orderSuccess.merchandiseAmount') }}</span>
                                <span class="price-value">{{ getCurrentPrice('merchandise') }}</span>
                            </div>

                            <!-- 运费 -->
                            <div class="price-row">
                                <span class="price-label">{{ t('orderSuccess.shippingAmount') }}</span>
                                <span class="price-value">{{ getCurrentPrice('shipping') }}</span>
                            </div>

                            <!-- 服务费 -->
                            <div class="price-row">
                                <span class="price-label">{{ t('orderSuccess.serviceFee') }}</span>
                                <span class="price-value">{{ getCurrentPrice('service') }}</span>
                            </div>

                            <!-- 折扣 -->
                            <div class="price-row" v-if="getDiscountAmount() > 0">
                                <span class="price-label">{{ t('orderSuccess.discountAmount') }}</span>
                                <span class="price-value discount">{{ getDiscountDisplay() }}</span>
                            </div>

                            <!-- 总金额 -->
                            <div class="price-row total-row">
                                <span class="price-label">{{ t('orderSuccess.totalAmount') }}</span>
                                <span class="price-value total">{{ getCurrentPrice('total') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <el-button @click="continueShopping">
                        {{ t('orderSuccess.continueShopping') }}
                    </el-button>
                    <el-button type="primary" @click="viewOrderList">
                        {{ t('orderSuccess.viewMyOrders') }}
                    </el-button>
                </div>

                <!-- 下一步提示 -->
                <div class="next-steps-card">
                    <h3 class="section-title">{{ t('orderSuccess.nextSteps') }}</h3>
                    <div class="steps-content">
                        <div class="step-item">
                            <div class="step-icon">1</div>
                            <div class="step-text">{{ t('orderSuccess.step1') }}</div>
                        </div>
                        <div class="step-item">
                            <div class="step-icon">2</div>
                            <div class="step-text">{{ t('orderSuccess.step2') }}</div>
                        </div>
                        <div class="step-item">
                            <div class="step-icon">3</div>
                            <div class="step-text">{{ t('orderSuccess.step3') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import CheckoutStepIndicator from '@/components/common/CheckoutStepIndicator.vue'
    import type { OrderSubmitResponse } from '@/api/modules/order'
    import type { CurrencyType } from '@/stores/currency'
    import { useCurrencyStore } from '@/stores/currency'
    import { ElMessage } from 'element-plus'
    import { computed, onMounted, ref } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRoute, useRouter } from 'vue-router'

    const router = useRouter()
    const route = useRoute()
    const { t } = useI18n()
    const currencyStore = useCurrencyStore()

    // 订单号（兼容性保留）
    const orderNumber = ref((route.query.orderNumber as string) || '')

    // 订单数据
    const orderData = ref<OrderSubmitResponse | null>(null)

    // 当前币种
    const currentCurrency = computed((): CurrencyType => currencyStore.getCurrentCurrency)

    // 获取当前币种对应的价格
    const getCurrentPrice = (priceType: 'merchandise' | 'shipping' | 'service' | 'discount' | 'total'): string => {
        if (!orderData.value?.priceDetails) return currencyStore.formatPriceWithSymbol(0)

        const priceDetails = orderData.value.priceDetails
        let amount = 0

        switch (priceType) {
            case 'merchandise':
                amount = currentCurrency.value === 'USD' ? priceDetails.merchandiseAmountUsd : priceDetails.merchandiseAmount
                break
            case 'shipping':
                amount = currentCurrency.value === 'USD' ? priceDetails.shippingAmountUsd : priceDetails.shippingAmount
                break
            case 'service':
                amount = currentCurrency.value === 'USD' ? priceDetails.serviceFeeUsd : priceDetails.serviceFee
                break
            case 'discount':
                amount = currentCurrency.value === 'USD' ? priceDetails.discountAmountUsd : priceDetails.discountAmount
                break
            case 'total':
                amount = currentCurrency.value === 'USD' ? priceDetails.totalAmountUsd : priceDetails.totalAmount
                break
        }

        return currencyStore.formatPriceWithSymbol(amount)
    }

    // 获取折扣金额数值用于条件渲染
    const getDiscountAmount = (): number => {
        if (!orderData.value?.priceDetails) return 0

        const priceDetails = orderData.value.priceDetails
        return currentCurrency.value === 'USD' ? priceDetails.discountAmountUsd : priceDetails.discountAmount
    }

    // 获取折扣显示（带负号）
    const getDiscountDisplay = (): string => {
        if (!orderData.value?.priceDetails) return currencyStore.formatPriceWithSymbol(0)

        const priceDetails = orderData.value.priceDetails
        const amount = currentCurrency.value === 'USD' ? priceDetails.discountAmountUsd : priceDetails.discountAmount

        // 确保显示为负数
        const displayAmount = Math.abs(amount)
        return `-${currencyStore.formatPriceWithSymbol(displayAmount)}`
    }

    // 在组件挂载时获取订单信息
    onMounted(() => {
        console.log('OrderSuccessView mounted, 检查订单数据...')

        // 先尝试从URL参数获取订单号
        const urlOrderNo = route.query.orderNo as string
        const urlOrderId = route.query.orderId as string

        if (urlOrderNo) {
            orderNumber.value = urlOrderNo
            console.log('从URL获取订单号:', urlOrderNo)
        }

        // 从localStorage获取订单详情
        const lastOrderData = localStorage.getItem('lastOrderSubmit')
        console.log('localStorage中的订单数据:', lastOrderData)

        if (!lastOrderData) {
            console.warn('未找到订单数据，重定向到首页')
            ElMessage.warning(t('orderSuccess.noOrderData'))
            router.push('/')
            return
        }

        try {
            const parsed = JSON.parse(lastOrderData)
            console.log('解析的订单数据:', parsed)

            orderData.value = parsed

            // 如果URL中没有订单号，使用订单对象中的
            if (!orderNumber.value && parsed?.purchaseOrderNo) {
                orderNumber.value = parsed.purchaseOrderNo
            }

            console.log('订单信息设置完成:', {
                orderNumber: orderNumber.value,
                orderData: orderData.value,
            })

            // 设置延迟清理localStorage，避免刷新页面丢失数据
            setTimeout(() => {
                localStorage.removeItem('lastOrderSubmit')
            }, 30000) // 30秒后清理
        } catch (error) {
            console.error('解析订单数据失败:', error)
            ElMessage.error(t('orderSuccess.dataParseError'))
            router.push('/')
        }
    })

    // 格式化金额
    const formatAmount = (amount: number | null | undefined) => {
        if (amount == null || isNaN(amount)) {
            return '0.00'
        }
        return amount.toFixed(2)
    }

    // 格式化日期
    const formatDate = (dateString?: string): string => {
        if (!dateString) {
            return new Date().toLocaleDateString()
        }

        try {
            const date = new Date(dateString)
            if (isNaN(date.getTime())) {
                return new Date().toLocaleDateString()
            }

            return new Intl.DateTimeFormat('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
            }).format(date)
        } catch (error) {
            console.error('日期格式化失败:', error)
            return new Date().toLocaleDateString()
        }
    }

    // 继续购物
    const continueShopping = () => {
        router.push('/')
    }

    // 查看订单列表
    const viewOrderList = () => {
        // 清除localStorage，因为导航后不再需要
        localStorage.removeItem('lastOrderSubmit')
        router.push('/user/track-orders')
    }
</script>

<style scoped lang="scss">
    .order-success-page {
        padding: 2rem 0;
        min-height: calc(100vh - 200px);
        background-color: #f8f9fa;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .success-content {
        max-width: 800px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .success-message-card {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .success-icon {
            margin: 0 auto 1.5rem;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: #f0fff0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #52c41a;
        }

        .success-title {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .success-description {
            font-size: 1.125rem;
            color: #666;
            margin-bottom: 1.5rem;
        }

        .order-number {
            background-color: #f9f9f9;
            border-radius: 4px;
            padding: 0.75rem;
            display: inline-block;

            .order-number-label {
                font-weight: 600;
                margin-right: 0.5rem;
            }

            .order-number-value {
                font-family: monospace;
                font-size: 1.125rem;
                color: #1890ff;
                font-weight: 600;
            }
        }
    }

    .order-details-card {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .details-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 1rem;
        }

        .order-info {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;

            @media (max-width: 768px) {
                flex-direction: column;
            }

            .info-section {
                flex: 1;
            }

            .section-title {
                font-size: 1.125rem;
                font-weight: 600;
                margin-bottom: 1rem;
                color: #333;
            }

            .info-content {
                color: #666;
            }

            .info-row {
                margin-bottom: 0.5rem;

                .info-label {
                    font-weight: 600;
                    margin-right: 0.5rem;
                }
            }

            .delivery-line {
                margin-bottom: 0.5rem;
                padding: 0.25rem 0;

                &:before {
                    content: '✓';
                    color: #52c41a;
                    font-weight: bold;
                    margin-right: 0.5rem;
                }
            }
        }

        .price-summary {
            .section-title {
                font-size: 1.125rem;
                font-weight: 600;
                margin-bottom: 1rem;
                color: #333;
            }

            .price-details {
                background-color: #f9f9f9;
                border-radius: 8px;
                padding: 1.5rem;

                .price-row {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1rem;
                    padding: 0.5rem 0;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &.total-row {
                        border-top: 2px solid #e8e8e8;
                        margin-top: 1rem;
                        padding-top: 1rem;
                        font-weight: 600;
                        font-size: 1.125rem;
                    }

                    .price-label {
                        font-weight: 500;
                        color: #333;
                    }

                    .price-value {
                        font-size: 1rem;
                        font-weight: 600;
                        color: #333;
                        text-align: right;

                        &.total {
                            font-size: 1.25rem;
                            color: #f56c6c;
                        }

                        &.discount {
                            color: #52c41a;
                        }
                    }
                }
            }
        }
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 1rem;

        @media (max-width: 768px) {
            flex-direction: column;
            align-items: center;

            .el-button {
                width: 100%;
                max-width: 300px;
            }
        }
    }

    .next-steps-card {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
            text-align: center;
        }

        .steps-content {
            display: flex;
            justify-content: space-between;
            gap: 1rem;

            @media (max-width: 768px) {
                flex-direction: column;
            }

            .step-item {
                flex: 1;
                text-align: center;
                padding: 1rem;

                .step-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background-color: #1890ff;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 600;
                    margin: 0 auto 1rem;
                }

                .step-text {
                    font-size: 0.875rem;
                    color: #666;
                    line-height: 1.4;
                }
            }
        }
    }
</style>
