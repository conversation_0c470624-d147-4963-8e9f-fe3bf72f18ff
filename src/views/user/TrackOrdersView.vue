<template>
    <div class="orders-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">{{ t('orders.title', '我的订单') }}</h1>
            <p class="page-subtitle">{{ t('orders.subtitle', '查看和管理您的订单') }}</p>
        </div>

        <!-- 筛选和搜索区域 -->
        <div class="filters-section">
            <!-- 订单状态筛选 -->
            <div class="status-filters">
                <button
                    v-for="statusFilter in statusFilters"
                    :key="statusFilter.value"
                    @click="setStatusFilter(statusFilter.value)"
                    class="status-filter-btn"
                    :class="{ active: currentStatus === statusFilter.value }"
                >
                    {{ statusFilter.label }}
                    <span v-if="statusFilter.count !== undefined" class="count">({{ statusFilter.count }})</span>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-section">
                <div class="search-input-group">
                    <input
                        v-model="searchKeyword"
                        type="text"
                        :placeholder="t('orders.search.placeholder', '搜索订单号或商品名称')"
                        class="search-input"
                        @keyup.enter="searchOrders"
                    />
                    <button @click="searchOrders" class="search-btn">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 订单列表 -->
        <div>
            <ul role="list" class="space-y-5">
                <li
                    v-for="order in orders"
                    :key="order.orderNo"
                    class="flex flex-col lg:flex-row bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow duration-300"
                >
                    <!-- Col 1: Brief Info (25%) -->
                    <div class="w-full lg:w-1/4 p-4 flex flex-col justify-center border-b lg:border-b-0 lg:border-r border-gray-200">
                        <div class="flex items-center gap-x-2 mb-2">
                            <p class="text-base font-bold leading-6 text-gray-900 break-all">{{ order.orderNo }}</p>
                            <span
                                class="inline-flex items-center rounded-full px-2.5 py-1 text-xs font-semibold"
                                :style="{ backgroundColor: order.orderStatusColor + '20', color: order.orderStatusColor }"
                            >
                                {{ getOrderStatusText(order.orderStatus) }}
                            </span>
                        </div>
                        <div class="space-y-1 text-sm">
                            <div class="flex items-center">
                                <span class="w-18 text-gray-500">{{ t('orders.quantityLabel', '数量:') }}</span>
                                <span class="font-medium text-gray-700">{{ t('orders.quantityValue', { quantity: order.totalQuantity, types: order.productTypeCount }) }}</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-14 text-gray-500">{{ t('orders.timeLabel', '时间:') }}</span>
                                <span class="font-medium text-gray-700">{{ formatDate(order.createTime) }}</span>
                            </div>
                        </div>
                        <div v-if="order.buyerMessage" class="mt-3">
                            <p class="text-sm text-gray-700 bg-blue-50 rounded-lg px-3 py-1.5 border-l-4 border-blue-400">
                                <span class="font-semibold text-blue-800">{{ t('orders.buyerMessageLabel', '留言:') }}</span>
                                {{ order.buyerMessage }}
                            </p>
                        </div>
                    </div>

                    <!-- Col 2: Progress (50%) -->
                    <div class="w-full lg:w-1/2 p-4 flex flex-col justify-center border-b lg:border-b-0 lg:border-r border-gray-200">
                        <div class="order-progress-steps">
                            <div class="text-center mb-2">
                                <span class="text-base font-bold text-gray-800">{{ t('orders.progressTitle', { percentage: order.progressPercentage }) }}</span>
                            </div>
                            <div class="flex w-full items-start">
                                <template v-for="(step, index) in getOrderSteps(order.orderStatus)" :key="step.status">
                                    <div class="flex flex-col items-center text-center">
                                        <div
                                            class="step-icon"
                                            :class="{
                                                'step-completed': step.completed,
                                                'step-current step-active': step.current,
                                                'step-pending': !step.completed && !step.current,
                                            }"
                                        >
                                            <component :is="getIconComponent(step.icon)" :size="16" class="step-icon-svg" />
                                        </div>
                                        <span
                                            class="step-title"
                                            :class="{
                                                'text-green-600': step.completed,
                                                'text-orange-600': step.current,
                                                'text-gray-500': !step.completed && !step.current,
                                            }"
                                        >
                                            {{ t(step.title) }}
                                        </span>
                                    </div>
                                    <div
                                        v-if="index < getOrderSteps(order.orderStatus).length - 1"
                                        class="flex-grow h-1 rounded-full transition-all duration-500 mx-1 sm:mx-2 mt-[1.125rem]"
                                        :class="step.completed ? 'bg-gradient-to-r from-green-500 to-green-400 shadow-sm' : 'bg-gray-200'"
                                    ></div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Col 3: Actions (25%) -->
                    <div class="w-full lg:w-1/4 p-4 flex flex-col items-start lg:items-end justify-between gap-y-2">
                        <div class="w-full text-left lg:text-right">
                            <p class="text-lg font-bold leading-6 text-gray-900">${{ order.totalAmountUsd.toFixed(2) }}</p>
                            <p class="text-sm leading-5 text-gray-600">¥{{ order.totalAmount.toFixed(2) }}</p>
                        </div>
                        <div class="w-full flex lg:justify-end gap-x-2">
                            <button
                                @click="handlePreview(order)"
                                class="flex-1 lg:flex-none rounded-lg bg-white px-4 py-2 text-sm font-medium text-orange-500 shadow-sm ring-1 ring-inset ring-orange-500 hover:bg-orange-50 hover:-translate-y-0.5 transition-all duration-200"
                            >
                                {{ t('orders.actions.preview', '预览') }}
                            </button>
                            <button
                                @click="viewOrderDetail(order.orderNo)"
                                class="flex-1 lg:flex-none rounded-lg bg-orange-500 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-orange-600 hover:-translate-y-0.5 transition-all duration-200"
                            >
                                {{ t('orders.actions.viewDetail', '详情') }}
                            </button>
                        </div>
                        <p class="w-full text-left lg:text-right text-xs leading-5 text-gray-500">{{ t('orders.lastUpdated', '更新:') }} {{ formatDate(order.updateTime) }}</p>
                    </div>
                </li>
            </ul>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && orders.length === 0" class="empty-state">
            <div class="empty-icon">
                <svg class="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                </svg>
            </div>
            <h3>{{ t('orders.empty.title', '暂无订单') }}</h3>
            <p>{{ t('orders.empty.description', '您还没有任何订单，去逛逛商城吧！') }}</p>
            <router-link to="/products" class="btn btn-primary">
                {{ t('orders.empty.browsing', '浏览商品') }}
            </router-link>
        </div>

        <!-- 分页器 -->
        <div v-if="pagination.totalElements > 0" class="pagination-container">
            <el-pagination
                v-model:current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.totalElements"
                layout="total, prev, pager, next, jumper"
                @current-change="changePage"
                background
            />
        </div>

        <!-- 商品预览弹窗 -->
        <el-dialog v-model="isPreviewModalVisible" :title="t('orders.previewModalTitle', '商品预览')" width="70%">
            <el-table v-if="!isPreviewLoading && previewOrderData?.orderItems" :data="previewOrderData.orderItems" stripe>
                <el-table-column :prop="productTitle" :label="t('orders.preview.productTitle', '商品标题')" />
                <el-table-column :label="t('orders.preview.specs', '规格')">
                    <template #default="{ row }">
                        <span v-for="spec in row.skuSpecs" :key="spec.attrKey">{{ spec.attrKey }}: {{ spec.attrValue }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="orderedQuantity" :label="t('orders.preview.quantity', '数量')" width="100" align="center" />
                <el-table-column prop="unitPriceUsd" :label="t('orders.preview.unitPrice', '单价')" width="120" align="right">
                    <template #default="{ row }">${{ row.unitPriceUsd.toFixed(2) }}</template>
                </el-table-column>
            </el-table>
            <div v-if="isPreviewLoading" v-loading="isPreviewLoading" style="height: 200px"></div>
            <div v-if="!isPreviewLoading && !previewOrderData?.orderItems">{{ t('orders.preview.loadError', '无法加载商品信息。') }}</div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
    import { ref, onMounted, computed } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter } from 'vue-router'
    import { orderApi, type OrderListRequest, type UserPurchaseOrderListVO } from '@/api/modules/order'
    import { ElMessage } from 'element-plus'
    import { CreditCard, CheckCircle, ShoppingCart, Clock, Truck, Loader, Home, Trophy, XCircle, HelpCircle, Save, Info } from 'lucide-vue-next'

    const { t } = useI18n()
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const orders = ref<UserPurchaseOrderListVO[]>([])
    const searchKeyword = ref('')
    const currentStatus = ref<number | undefined>(undefined)

    // 预览弹窗状态
    const isPreviewModalVisible = ref(false)
    const isPreviewLoading = ref(false)
    const previewOrderData = ref<any>(null)

    // 分页数据
    const pagination = ref({
        page: 1,
        size: 10,
        totalElements: 0,
        totalPages: 1,
    })

    // 订单状态筛选选项 (改为计算属性以支持i18n动态更新)
    const statusFilters = computed(() => [
        { label: t('orders.status.all'), value: undefined, count: undefined },
        { label: t('orders.status.pending'), value: 1, count: undefined },
        { label: t('orders.status.paid'), value: 2, count: undefined },
        { label: t('orders.status.purchasing'), value: 3, count: undefined },
        { label: t('orders.status.shipped'), value: 4, count: undefined },
        { label: t('orders.status.inWarehouse'), value: 5, count: undefined },
        { label: t('orders.status.completed'), value: 6, count: undefined },
        { label: t('orders.status.cancelled'), value: -1, count: undefined },
    ])

    // 图标映射
    const iconMap = {
        save: Save,
        'credit-card': CreditCard,
        'check-circle': CheckCircle,
        'shopping-cart': ShoppingCart,
        'clock-circle': Clock,
        truck: Truck,
        loading: Loader,
        home: Home,
        trophy: Trophy,
        'close-circle': XCircle,
        'question-circle': HelpCircle,
        'info-circle': Info,
    }

    // 方法
    const getIconComponent = (iconName: string) => {
        return iconMap[iconName as keyof typeof iconMap] || Info
    }

    const getOrderStatusText = (status: number): string => {
        const statusEntry = statusFilters.value.find(f => f.value === status)
        return statusEntry ? statusEntry.label : ''
    }

    // 获取订单步骤
    const getOrderSteps = (orderStatus: number) => {
        const steps = [
            { title: 'orders.status.pending', icon: 'credit-card', status: 1 },
            { title: 'orders.status.paid', icon: 'check-circle', status: 2 },
            { title: 'orders.status.purchasing', icon: 'shopping-cart', status: 3 },
            { title: 'orders.status.shipped', icon: 'truck', status: 4 },
            { title: 'orders.status.inWarehouse', icon: 'home', status: 5 },
            { title: 'orders.status.completed', icon: 'trophy', status: 6 },
        ]

        return steps.map(step => ({
            ...step,
            completed: orderStatus > step.status,
            current: orderStatus === step.status,
        }))
    }

    const loadOrders = async (params: OrderListRequest = {}) => {
        loading.value = true
        try {
            const result = await orderApi.getOrderList({
                page: pagination.value.page,
                size: pagination.value.size,
                status: currentStatus.value,
                keyword: searchKeyword.value,
                ...params,
            })

            if (result && typeof result.total === 'number' && Array.isArray(result.records)) {
                orders.value = result.records
                pagination.value.page = result.pageIndex
                pagination.value.size = result.pageSize
                pagination.value.totalElements = result.total
                pagination.value.totalPages = result.pageSize > 0 ? Math.ceil(result.total / result.pageSize) : 1
            } else {
                orders.value = []
                pagination.value.totalElements = 0
                pagination.value.page = 1
                pagination.value.totalPages = 1
            }
        } catch (error) {
            console.error('加载订单列表失败:', error)
            ElMessage.error(t('orders.error.loadFailed', '加载订单列表失败'))
        } finally {
            loading.value = false
        }
    }

    const setStatusFilter = (status: number | undefined) => {
        currentStatus.value = status
        pagination.value.page = 1
        loadOrders()
    }

    const searchOrders = () => {
        pagination.value.page = 1
        loadOrders()
    }

    const changePage = (page: number) => {
        pagination.value.page = page
        loadOrders()
    }

    const formatDate = (dateString: string) => {
        if (!dateString) return ''
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        })
    }

    const viewOrderDetail = (orderNo: string) => {
        router.push(`/user/orders/${orderNo}`)
    }

    // 预览商品
    const handlePreview = async (order: UserPurchaseOrderListVO) => {
        isPreviewModalVisible.value = true
        isPreviewLoading.value = true
        previewOrderData.value = null
        try {
            const data = await orderApi.getOrderDetail(order.orderNo)
            previewOrderData.value = data
        } catch (error) {
            ElMessage.error(t('orders.preview.loadError', '加载商品信息失败'))
            console.error('Failed to fetch order details for preview:', error)
        } finally {
            isPreviewLoading.value = false
        }
    }

    // 生命周期
    onMounted(() => {
        loadOrders()
    })
</script>

<style scoped lang="scss">
    .orders-container {
        background-color: #f9fafb;
        min-height: 100vh;
        padding: 1rem;

        @media (min-width: 768px) {
            padding: 2rem 4rem;
        }
    }

    .page-header {
        text-align: left;
        margin-bottom: 2rem;

        .page-title {
            font-size: 2.25rem;
            font-weight: 700;
            color: #111827;
        }

        .page-subtitle {
            color: #6b7280;
            font-size: 1rem;
            margin-top: 0.5rem;
        }
    }

    .filters-section {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        @media (min-width: 1024px) {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }

    .status-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;

        .status-filter-btn {
            padding: 0.6rem 1.25rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            background: white;
            color: #374151;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease-in-out;

            &:hover {
                border-color: #fb923c;
                background-color: #fff7ed;
                color: #c2410c;
            }

            &.active {
                border-color: #f97316;
                background: #f97316;
                color: white;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .count {
                margin-left: 0.35rem;
                font-size: 0.8rem;
                color: #6b7280;
            }

            &.active .count {
                color: white;
                opacity: 0.8;
            }
        }
    }

    .search-section {
        .search-input-group {
            display: flex;
            width: 100%;

            @media (min-width: 1024px) {
                max-width: 350px;
            }

            .search-input {
                flex-grow: 1;
                padding: 0.75rem 1rem;
                border: 1px solid #d1d5db;
                border-right: none;
                border-radius: 0.5rem 0 0 0.5rem;
                outline: none;
                transition: border-color 0.2s;

                &:focus {
                    border-color: #f97316;
                    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
                }
            }

            .search-btn {
                padding: 0.75rem 1rem;
                background: #f97316;
                color: white;
                border: 1px solid #f97316;
                border-radius: 0 0.5rem 0.5rem 0;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;

                &:hover {
                    background: #ea580c;
                }
            }
        }
    }

    // 订单进度步骤样式
    .order-progress-steps {
        .step-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            border: 3px solid;
            transition: all 0.3s ease-in-out;
            position: relative;
            z-index: 10;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);

            &.step-completed {
                background: linear-gradient(135deg, #10b981, #059669);
                border-color: #10b981;
                color: white;
                box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
            }

            &.step-current {
                background: linear-gradient(135deg, #f97316, #ea580c);
                border-color: #f97316;
                color: white;
                box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);

                &.step-active {
                    animation: pulse-orange 2s infinite;
                }
            }

            &.step-pending {
                background-color: white;
                border-color: #d1d5db;
                color: #9ca3af;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .step-icon-svg {
                transition: transform 0.2s ease-in-out;
            }
        }

        .step-title {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
        }
    }

    // 动画效果
    @keyframes pulse-orange {
        0%,
        100% {
            box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.3);
            transform: scale(1);
        }
        50% {
            box-shadow: 0 0 0 8px rgba(249, 115, 22, 0.15);
            transform: scale(1.05);
        }
    }

    // 空状态样式
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;

        .empty-icon {
            margin-bottom: 1.5rem;
            color: #d1d5db;
        }

        h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        p {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 2rem;
            background: #f97316;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: background-color 0.2s;

            &:hover {
                background: #ea580c;
            }
        }
    }

    // 分页样式
    .pagination-container {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 2rem;

        :deep(.el-pagination) {
            background: white;
            padding: 1rem;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
    }
</style>
