<template>
    <div class="orders-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">{{ t('orders.title', '我的订单') }}</h1>
            <p class="page-subtitle">{{ t('orders.subtitle', '查看和管理您的订单') }}</p>
        </div>

        <!-- 筛选和搜索区域 -->
        <div class="filters-section">
            <!-- 订单状态筛选 -->
            <div class="status-filters">
                <button
                    v-for="statusFilter in statusFilters"
                    :key="statusFilter.value"
                    @click="setStatusFilter(statusFilter.value)"
                    class="status-filter-btn"
                    :class="{ active: currentStatus === statusFilter.value }"
                >
                    {{ statusFilter.label }}
                    <span v-if="statusFilter.count !== undefined" class="count">({{ statusFilter.count }})</span>
                </button>
            </div>

            <!-- 搜索栏 -->
            <div class="search-section">
                <div class="search-input-group">
                    <input
                        v-model="searchKeyword"
                        type="text"
                        :placeholder="t('orders.search.placeholder', '搜索订单号或商品名称')"
                        class="search-input"
                        @keyup.enter="searchOrders"
                    />
                    <button @click="searchOrders" class="search-btn">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>{{ t('orders.loading', '加载中...') }}</p>
        </div>

        <!-- 订单列表 -->
        <div v-else-if="orders.length > 0" class="orders-list">
            <div v-for="order in orders" :key="order.orderNo" class="order-card">
                <!-- 订单头部信息 -->
                <div class="order-header">
                    <div class="order-info">
                        <h3 class="order-number">{{ t('orders.orderNo', '订单号') }}: {{ order.orderNo }}</h3>
                        <div class="order-meta">
                            <span class="order-status" :class="`status-${order.orderStatus}`">
                                {{ order.orderStatusText }}
                            </span>
                            <span class="order-date">{{ formatDate(order.createTime) }}</span>
                        </div>
                    </div>
                    <div class="order-amount">
                        <div class="amount-display">
                            <span class="currency">{{ order.currency }}</span>
                            <span class="amount">${{ order.totalAmountUsd.toFixed(2) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 主要商品信息 -->
                <div class="order-content">
                    <div class="product-info">
                        <div class="product-image">
                            <img :src="order.mainProductImageUrl" :alt="order.mainProductTitle" />
                        </div>
                        <div class="product-details">
                            <h4 class="product-title">{{ order.mainProductTitle }}</h4>
                            <p class="product-subtitle">{{ order.mainProductTitleEn }}</p>
                            <div class="product-meta">
                                <span class="quantity">{{ t('orders.quantity', '数量') }}: {{ order.totalQuantity }}</span>
                                <span class="types">{{ order.productTypeCount }} {{ t('orders.types', '种商品') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 买家留言 -->
                    <div v-if="order.buyerMessage" class="buyer-message">
                        <p class="message-label">{{ t('orders.buyerMessage', '买家留言') }}:</p>
                        <p class="message-content">{{ order.buyerMessage }}</p>
                    </div>
                </div>

                <!-- 订单操作按钮 -->
                <div class="order-actions">
                    <router-link :to="`/user/orders/${order.orderNo}`" class="btn btn-outline">
                        {{ t('orders.actions.viewDetail', '查看详情') }}
                    </router-link>

                    <button v-if="order.canViewTracking" @click="viewTracking(order.orderNo)" class="btn btn-outline">
                        {{ t('orders.actions.viewTracking', '查看物流') }}
                    </button>

                    <button v-if="order.canPay" @click="payOrder(order.orderNo)" class="btn btn-primary">
                        {{ t('orders.actions.pay', '立即支付') }}
                    </button>

                    <button v-if="order.canConfirmReceipt" @click="confirmReceipt(order.orderNo)" class="btn btn-success">
                        {{ t('orders.actions.confirmReceipt', '确认收货') }}
                    </button>

                    <button v-if="order.canCancel" @click="cancelOrder(order.orderNo)" class="btn btn-danger">
                        {{ t('orders.actions.cancel', '取消订单') }}
                    </button>

                    <button v-if="order.canApplyRefund" @click="applyRefund(order.orderNo)" class="btn btn-warning">
                        {{ t('orders.actions.refund', '申请退款') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
            <div class="empty-icon">
                <svg class="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="1"
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                </svg>
            </div>
            <h3>{{ t('orders.empty.title', '暂无订单') }}</h3>
            <p>{{ t('orders.empty.description', '您还没有任何订单，去逛逛商城吧！') }}</p>
            <router-link to="/products" class="btn btn-primary">
                {{ t('orders.empty.browsing', '浏览商品') }}
            </router-link>
        </div>

        <!-- 分页器 -->
        <div v-if="pagination.totalPages > 1" class="pagination-container">
            <nav class="pagination">
                <button :disabled="pagination.page === 1" @click="changePage(pagination.page - 1)" class="pagination-btn">
                    {{ t('common.previous', '上一页') }}
                </button>

                <div class="pagination-pages">
                    <button v-for="page in visiblePages" :key="page" @click="changePage(page)" class="pagination-page" :class="{ active: page === pagination.page }">
                        {{ page }}
                    </button>
                </div>

                <button :disabled="pagination.page === pagination.totalPages" @click="changePage(pagination.page + 1)" class="pagination-btn">
                    {{ t('common.next', '下一页') }}
                </button>
            </nav>

            <div class="pagination-info">
                {{ t('common.pagination.showing', '显示') }} {{ (pagination.page - 1) * pagination.size + 1 }}-{{
                    Math.min(pagination.page * pagination.size, pagination.totalElements)
                }}
                {{ t('common.pagination.of', '共') }} {{ pagination.totalElements }} {{ t('common.pagination.items', '条') }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { ref, onMounted, computed } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter } from 'vue-router'
    import { orderApi, type OrderListRequest, type UserPurchaseOrderListVO, type PageDTO } from '@/api/modules/order'
    import { ElMessage, ElMessageBox } from 'element-plus'

    const { t } = useI18n()
    const router = useRouter()

    // 响应式数据
    const loading = ref(false)
    const orders = ref<UserPurchaseOrderListVO[]>([])
    const searchKeyword = ref('')
    const currentStatus = ref<number | undefined>(undefined)

    // 分页数据
    const pagination = ref<PageDTO<UserPurchaseOrderListVO>>({
        content: [],
        totalElements: 0,
        totalPages: 0,
        page: 1,
        size: 10,
        first: true,
        last: true,
        empty: true,
    })

    // 订单状态筛选选项
    const statusFilters = ref([
        { label: t('orders.status.all', '全部'), value: undefined, count: undefined },
        { label: t('orders.status.pending', '待支付'), value: 1, count: undefined },
        { label: t('orders.status.paid', '已支付'), value: 2, count: undefined },
        { label: t('orders.status.shipped', '已发货'), value: 3, count: undefined },
        { label: t('orders.status.delivered', '已送达'), value: 4, count: undefined },
        { label: t('orders.status.completed', '已完成'), value: 5, count: undefined },
        { label: t('orders.status.cancelled', '已取消'), value: -1, count: undefined },
    ])

    // 计算属性：可见的页码
    const visiblePages = computed(() => {
        const totalPages = pagination.value.totalPages
        const currentPage = pagination.value.page
        const pages: number[] = []

        if (totalPages <= 7) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i)
            }
        } else {
            if (currentPage <= 4) {
                for (let i = 1; i <= 5; i++) {
                    pages.push(i)
                }
                pages.push(-1) // 表示省略号
                pages.push(totalPages)
            } else if (currentPage >= totalPages - 3) {
                pages.push(1)
                pages.push(-1)
                for (let i = totalPages - 4; i <= totalPages; i++) {
                    pages.push(i)
                }
            } else {
                pages.push(1)
                pages.push(-1)
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i)
                }
                pages.push(-1)
                pages.push(totalPages)
            }
        }

        return pages
    })

    // 方法
    const loadOrders = async (params: OrderListRequest = {}) => {
        console.log('1. Starting loadOrders...')
        loading.value = true
        try {
            console.log('2. Calling orderApi.getOrderList with params:', {
                page: pagination.value.page,
                size: pagination.value.size,
                status: currentStatus.value,
                keyword: searchKeyword.value,
                ...params,
            })
            const result = await orderApi.getOrderList({
                page: pagination.value.page,
                size: pagination.value.size,
                status: currentStatus.value,
                keyword: searchKeyword.value,
                ...params,
            })
            console.log('3. Received result from API:', result)

            if (result) {
                orders.value = result.content
                pagination.value = result
                console.log('4. Updated orders and pagination state.')
            } else {
                console.error('5. API returned undefined or null result.')
                ElMessage.error(t('orders.error.loadFailed', '加载订单列表失败，未收到有效数据'))
            }
        } catch (error) {
            console.error('6. Caught an error in loadOrders:', error)
            ElMessage.error(t('orders.error.loadFailed', '加载订单列表失败'))
        } finally {
            loading.value = false
            console.log('7. Finished loadOrders, loading is now false.')
        }
    }

    const setStatusFilter = (status: number | undefined) => {
        currentStatus.value = status
        pagination.value.page = 1
        loadOrders()
    }

    const searchOrders = () => {
        pagination.value.page = 1
        loadOrders()
    }

    const changePage = (page: number) => {
        if (page >= 1 && page <= pagination.value.totalPages) {
            pagination.value.page = page
            loadOrders()
        }
    }

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        })
    }

    // 订单操作方法
    const viewTracking = (orderNo: string) => {
        router.push(`/user/orders/${orderNo}/tracking`)
    }

    const payOrder = (orderNo: string) => {
        router.push(`/user/orders/${orderNo}/pay`)
    }

    const confirmReceipt = async (orderNo: string) => {
        try {
            await ElMessageBox.confirm(t('orders.confirm.receipt', '确认已收到商品吗？'), t('orders.confirm.title', '确认操作'), {
                confirmButtonText: t('common.confirm', '确认'),
                cancelButtonText: t('common.cancel', '取消'),
                type: 'warning',
            })

            await orderApi.confirmReceipt(orderNo)
            ElMessage.success(t('orders.success.confirmed', '确认收货成功'))
            loadOrders()
        } catch (error) {
            if (error !== 'cancel') {
                console.error('确认收货失败:', error)
                ElMessage.error(t('orders.error.confirmFailed', '确认收货失败'))
            }
        }
    }

    const cancelOrder = async (orderNo: string) => {
        try {
            const { value: reason } = await ElMessageBox.prompt(t('orders.cancel.reason', '请输入取消原因'), t('orders.cancel.title', '取消订单'), {
                confirmButtonText: t('common.confirm', '确认'),
                cancelButtonText: t('common.cancel', '取消'),
                inputPlaceholder: t('orders.cancel.placeholder', '请输入取消原因（可选）'),
            })

            await orderApi.cancelOrder(orderNo, reason)
            ElMessage.success(t('orders.success.cancelled', '订单取消成功'))
            loadOrders()
        } catch (error) {
            if (error !== 'cancel') {
                console.error('取消订单失败:', error)
                ElMessage.error(t('orders.error.cancelFailed', '取消订单失败'))
            }
        }
    }

    const applyRefund = (orderNo: string) => {
        router.push(`/user/orders/${orderNo}/refund`)
    }

    // 生命周期
    onMounted(() => {
        loadOrders()
    })
</script>

<style scoped lang="scss">
    .orders-container {
        background-color: #f9fafb; // Light gray background
        min-height: 100vh;
        padding: 1rem;

        @media (min-width: 768px) {
            padding: 2rem 4rem;
        }
    }

    .page-header {
        text-align: left; // Align to left
        margin-bottom: 2rem;

        .page-title {
            font-size: 2.25rem; // Larger title
            font-weight: 700;
            color: #111827;
        }

        .page-subtitle {
            color: #6b7280;
            font-size: 1rem;
            margin-top: 0.5rem;
        }
    }

    .filters-section {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem; // Increased margin
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        @media (min-width: 1024px) {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }

    .status-filters {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem; // Increased gap

        .status-filter-btn {
            padding: 0.6rem 1.25rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            background: white;
            color: #374151;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease-in-out;

            &:hover {
                border-color: #fb923c;
                background-color: #fff7ed;
                color: #c2410c;
            }

            &.active {
                border-color: #f97316;
                background: #f97316;
                color: white;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            .count {
                margin-left: 0.35rem;
                font-size: 0.8rem;
                color: #6b7280;
            }

            &.active .count {
                color: white;
                opacity: 0.8;
            }
        }
    }

    .search-section {
        .search-input-group {
            display: flex;
            width: 100%;

            @media (min-width: 1024px) {
                max-width: 350px;
            }

            .search-input {
                flex-grow: 1;
                padding: 0.75rem 1rem;
                border: 1px solid #d1d5db;
                border-right: none;
                border-radius: 0.5rem 0 0 0.5rem;
                outline: none;
                transition: border-color 0.2s;

                &:focus {
                    border-color: #f97316;
                    box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.2);
                }
            }

            .search-btn {
                padding: 0.75rem 1rem;
                background: #f97316;
                color: white;
                border: 1px solid #f97316;
                border-radius: 0 0.5rem 0.5rem 0;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s;

                &:hover {
                    background: #ea580c;
                }
            }
        }
    }

    .loading-container,
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 5rem 1rem;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    }

    .loading-container {
        .loading-spinner {
            width: 3rem;
            height: 3rem;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #f97316;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        p {
            font-size: 1rem;
            color: #4b5563;
        }
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .orders-list {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .order-card {
        background: white;
        border-radius: 0.75rem;
        padding: 0; // Remove padding here
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        transition: all 0.2s ease-in-out;
        overflow: hidden;

        &:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
        flex-wrap: wrap;
        gap: 1rem;

        .order-info {
            .order-number {
                font-size: 1rem;
                font-weight: 600;
                color: #111827;
                margin-right: 1rem;
            }

            .order-meta {
                display: flex;
                gap: 1rem;
                align-items: center;
                margin-top: 0.5rem;

                .order-status {
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    font-weight: 500;
                    text-transform: uppercase;

                    &.status-1 {
                        background-color: #fef3c7;
                        color: #92400e;
                    } // 待支付
                    &.status-2 {
                        background-color: #dbeafe;
                        color: #1e40af;
                    } // 已支付
                    &.status-3 {
                        background-color: #e9d5ff;
                        color: #5b21b6;
                    } // 已发货
                    &.status-4 {
                        background-color: #dcfce7;
                        color: #166534;
                    } // 已送达
                    &.status-5 {
                        background-color: #cffafe;
                        color: #0891b2;
                    } // 已完成
                    &.status--1 {
                        background-color: #fee2e2;
                        color: #991b1b;
                    } // 已取消
                }

                .order-date {
                    color: #6b7280;
                    font-size: 0.875rem;
                }
            }
        }

        .order-amount {
            .amount-display {
                text-align: right;

                .amount {
                    font-size: 1.5rem;
                    font-weight: 700;
                    color: #111827;
                }
                .currency {
                    font-size: 1rem;
                    font-weight: 500;
                    color: #6b7280;
                    margin-right: 0.25rem;
                }
            }
        }
    }

    .order-content {
        padding: 1.5rem;

        .product-info {
            display: flex;
            gap: 1.5rem;

            .product-image {
                flex-shrink: 0;
                width: 5rem;
                height: 5rem;
                border-radius: 0.5rem;
                overflow: hidden;
                border: 1px solid #e5e7eb;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .product-details {
                flex: 1;

                .product-title {
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: #1f2937;
                    margin-bottom: 0.25rem;
                    line-height: 1.4;
                }

                .product-subtitle {
                    font-size: 0.875rem;
                    color: #6b7280;
                    margin-bottom: 0.5rem;
                    line-height: 1.4;
                }

                .product-meta {
                    display: flex;
                    gap: 1rem;
                    font-size: 0.875rem;
                    color: #6b7280;
                    margin-top: 0.5rem;
                }
            }
        }

        .buyer-message {
            margin-top: 1rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 0.5rem;
            border-left: 4px solid #f97316;

            .message-label {
                font-size: 0.875rem;
                font-weight: 600;
                color: #374151;
                margin-bottom: 0.25rem;
            }

            .message-content {
                font-size: 0.875rem;
                color: #6b7280;
            }
        }
    }

    .order-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: flex-end;
        padding: 0 1.5rem 1.5rem;
    }

    .btn {
        padding: 0.6rem 1.25rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        border: 1px solid transparent;

        &.btn-outline {
            border-color: #d1d5db;
            background: white;
            color: #374151;

            &:hover {
                background: #f9fafb;
                border-color: #9ca3af;
            }
        }

        &.btn-primary {
            background: #f97316;
            color: white;
            border-color: #f97316;

            &:hover {
                background: #ea580c;
                border-color: #ea580c;
            }
        }

        &.btn-success,
        &.btn-danger,
        &.btn-warning {
            color: white;
        }

        &.btn-success {
            background: #16a34a;
            border-color: #16a34a;
            &:hover {
                background: #15803d;
                border-color: #15803d;
            }
        }

        &.btn-danger {
            background: #dc2626;
            border-color: #dc2626;
            &:hover {
                background: #b91c1c;
                border-color: #b91c1c;
            }
        }

        &.btn-warning {
            background: #d97706;
            border-color: #d97706;
            &:hover {
                background: #b45309;
                border-color: #b45309;
            }
        }
    }

    .empty-state {
        .empty-icon {
            color: #9ca3af;
            margin-bottom: 1rem;
            svg {
                width: 4rem;
                height: 4rem;
            }
        }

        h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #111827;
        }

        p {
            color: #6b7280;
            margin-top: 0.5rem;
            margin-bottom: 2rem;
            max-width: 400px;
        }
    }

    .pagination-container {
        margin-top: 2rem;
        padding: 1.5rem;
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;

        @media (min-width: 768px) {
            flex-direction: row;
            justify-content: space-between;
        }
    }

    .pagination {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .pagination-btn,
        .pagination-page {
            height: 2.5rem;
            min-width: 2.5rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0 1rem;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 500;

            &:hover:not(:disabled) {
                background: #f9fafb;
                border-color: #9ca3af;
            }

            &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                background-color: #f9fafb;
            }
        }

        .pagination-pages {
            display: flex;
            gap: 0.5rem;

            .pagination-page {
                &.active {
                    background: #f97316;
                    color: white;
                    border-color: #f97316;
                    font-weight: 600;
                }
            }
        }
    }

    .pagination-info {
        color: #4b5563;
        font-size: 0.875rem;
    }

    // 响应式设计
    @media (max-width: 768px) {
        .order-header {
            flex-direction: column;
            gap: 1rem;

            .order-amount {
                align-self: flex-start;
            }
        }

        .product-info {
            flex-direction: column;

            .product-image {
                align-self: center;
                width: 6rem;
                height: 6rem;
            }
        }

        .order-actions {
            justify-content: center;
        }

        .status-filters {
            justify-content: center;
        }
    }
</style>
