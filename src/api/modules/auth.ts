import api from '@/api/config/axios'
import type { ApiResponse, LoginRequest, LoginUserInfoRes, RegisterRequest } from '@/api/types'
import { handleApiError, normalizeResponse } from '@/api/utils/apiUtils'

/**
 * 认证API路径常量
 */
const AUTH_API_PATHS = {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REGISTER: '/api/auth/register',
    EMAIL_CAPTCHA: '/api/auth/email-captcha',
    ACTIVATION: '/api/auth/activation',
    LOGIN_BY_WMS: '/api/auth/loginByWms',
}

/**
 * 认证API模块
 * 提供用户认证相关的所有API功能
 */
export const authApi = {
    /**
     * 用户登录
     * @param data 登录请求数据
     * @returns 登录用户信息
     */
    async login(data: LoginRequest): Promise<LoginUserInfoRes> {
        try {
            const response = await api.post<ApiResponse<LoginUserInfoRes>>(AUTH_API_PATHS.LOGIN, data)
            return normalizeResponse<LoginUserInfoRes>(response.data)
        } catch (error) {
            return handleApiError(error, 'login failed')
        }
    },

    /**
     * 通过WMS登录
     * <pre>
     * wms 跳转到页面，页面中需要调用此接口
     * TODO：wms 页面
     * 请求参数:
     * {
     *     "code": "string"
     * }
     * </pre>
     * @param data 登录请求数据
     * @returns 登录用户信息
     */
    async loginByWms(code: string, cusCode?: string): Promise<LoginUserInfoRes> {
        try {
            const response = await api.get<ApiResponse<LoginUserInfoRes>>(AUTH_API_PATHS.LOGIN_BY_WMS, { params: { code, cusCode } })
            return normalizeResponse<LoginUserInfoRes>(response.data)
        } catch (error) {
            return handleApiError(error, 'login failed')
        }
    },

    /**
     * 用户退出登录
     * @returns 操作结果
     */
    async logout(): Promise<string> {
        try {
            const response = await api.post<ApiResponse<string>>(AUTH_API_PATHS.LOGOUT)
            return normalizeResponse<string>(response.data)
        } catch (error) {
            return handleApiError(error, 'logout failed')
        }
    },

    /**
     * 用户注册
     * @param data 注册请求数据
     * @returns 操作结果
     */
    async register(data: RegisterRequest): Promise<string> {
        try {
            const response = await api.post<ApiResponse<string>>(AUTH_API_PATHS.REGISTER, data)
            return normalizeResponse<string>(response.data)
        } catch (error) {
            return handleApiError(error, 'register failed')
        }
    },

    /**
     * 重新发送邮箱验证码
     * @param data 请求参数
     * @returns 操作结果
     */
    async resendEmailCaptcha(data: { email: string }): Promise<string> {
        try {
            const response = await api.get<ApiResponse<string>>(AUTH_API_PATHS.EMAIL_CAPTCHA, { params: data })
            return normalizeResponse<string>(response.data)
        } catch (error) {
            return handleApiError(error, 'send email captcha failed')
        }
    },

    /**
     * 激活用户
     * @param uid 用户ID
     * @param code 激活码
     * @returns 操作结果
     */
    async activation(uid: string, code: string): Promise<string> {
        try {
            const response = await api.get<ApiResponse<string>>(`${AUTH_API_PATHS.ACTIVATION}/${uid}`, { params: { code } })
            return normalizeResponse<string>(response.data)
        } catch (error) {
            return handleApiError(error, 'activation failed')
        }
    },
}
