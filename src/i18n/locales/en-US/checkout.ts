export default {
    title: 'Checkout',
    steps: {
        shipping: 'Shipping',
        payment: 'Payment',
        review: 'Review'
    },
    underDevelopment: 'Checkout is under development',
    comingSoon: 'We are working hard to bring you a smooth checkout experience. This feature will be available soon.',
    backToCart: 'Back to Cart',
    orderPreviewMode: 'Order Preview Mode',
    orderPreviewDescription: 'Processing your buy now order',
    shippingAddress: 'Shipping Address',
    addAddress: 'Add New Address',
    useThisAddress: 'Use this address',
    orderNotes: 'Order Notes',
    fullName: 'Full Name',
    phoneNumber: 'Phone Number',
    address: 'Address',
    city: 'City',
    state: 'State/Province',
    zipCode: 'ZIP/Postal Code',
    paymentMethod: 'Payment Method',
    creditCard: 'Credit Card',
    bankTransfer: 'Bank Transfer',
    orderItems: 'Order Items',
    orderSummary: 'Order Summary',
    subtotal: 'Subtotal',
    shippingFee: 'Shipping Fee',
    tax: 'Tax',
    serviceFee: 'Service Fee',
    total: 'Total',
    totalQuantity: 'Total Quantity',
    totalAmount: 'Total Amount',
    quantity: 'Quantity',
    unitPrice: 'Unit Price',
    placeOrder: 'Place Order',
    submitOrder: 'Submit Order',
    orderPlaced: 'Order submitted successfully',
    orderFailed: 'Order submission failed, please try again',
    termsAgreement: 'I agree to the',
    termsAndConditions: 'Terms and Conditions',
    completeAllFields: 'Please complete all required fields',
    noItemsSelected: 'No items selected for checkout',
    invalidAccessTitle: 'Unable to Process Order',
    invalidAccessMessage: 'Please add items to your cart or use "Buy Now" to proceed.',
    previewValid: 'Preview Valid',
    previewExpired: 'Preview Expired',
    remainingTime: 'Remaining Time',
    expired: 'Expired',
    pleaseRefreshPreview: 'Please refresh preview',
    expiredMessage: 'Order preview has expired. Please refresh the preview to get the latest price and inventory information.',
    refreshPreview: 'Refresh Preview',
    previewDataExpired: 'Order preview data has expired, will use cart data',
    previewDataError: 'Failed to get order preview',
    tokenExpired: 'Order preview has expired, please preview again',
    orderNotePlaceholder: 'Please enter your special requirements or notes'
}