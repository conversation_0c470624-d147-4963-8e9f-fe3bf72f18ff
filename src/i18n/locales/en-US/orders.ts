export default {
    title: 'My Orders',
    subtitle: 'View and manage your orders',
    search: {
        placeholder: 'Search by order number or product name',
    },
    loading: 'Loading...',
    orderNo: 'Order No',
    orderInfo: 'Order Info',
    progressAmount: 'Progress & Amount',
    quantity: 'Quantity',
    types: 'types of products',
    buyerMessage: 'Buyer Message',
    totalQuantity: 'Total Quantity',
    productTypes: 'Product Types',
    orderStatus: 'Order Status',
    completion: 'Completion',
    quantityLabel: 'Quantity: ',
    quantityValue: '{quantity} items · {types} types',
    timeLabel: 'Time: ',
    buyerMessageLabel: 'Message: ',
    progressTitle: 'Order Progress {percentage}%',
    lastUpdated: 'Updated: ',
    previewModalTitle: 'Product Preview',
    empty: {
        title: 'No Orders Yet',
        description: "You don't have any orders yet. Let's go shopping!",
        browsing: 'Browse Products',
    },
    status: {
        all: 'All',
        pending: 'Pending Payment',
        paid: 'Paid',
        purchasing: 'Purchasing',
        shipped: 'Shipped',
        delivered: 'Delivered',
        inWarehouse: 'In Warehouse',
        completed: 'Completed',
        cancelled: 'Cancelled',
        label: 'Status',
    },
    product: {
        label: 'Product',
    },
    purchaseInfo: {
        label: 'Purchase Info',
    },
    amount: {
        label: 'Amount',
    },
    createTime: {
        label: 'Order Time',
    },
    items: {
        title: 'Order Items',
    },
    actions: {
        label: 'Actions',
        viewDetail: 'Details',
        viewTracking: 'Track Package',
        pay: 'Pay Now',
        confirmReceipt: 'Confirm Receipt',
        cancel: 'Cancel Order',
        refund: 'Apply for Refund',
        more: 'More',
        preview: 'Preview',
    },
    error: {
        loadFailed: 'Failed to load order list',
        confirmFailed: 'Failed to confirm receipt',
        cancelFailed: 'Failed to cancel order',
    },
    confirm: {
        title: 'Confirm Action',
        receipt: 'Are you sure you have received the products?',
    },
    cancel: {
        title: 'Cancel Order',
        reason: 'Please enter the reason for cancellation',
        placeholder: 'Enter reason for cancellation (optional)',
    },
    success: {
        confirmed: 'Receipt confirmed successfully',
        cancelled: 'Order cancelled successfully',
    },
    preview: {
        productTitle: 'Product Title',
        specs: 'Specifications',
        quantity: 'Quantity',
        unitPrice: 'Unit Price',
        loadError: 'Failed to load product information.',
    },
} 