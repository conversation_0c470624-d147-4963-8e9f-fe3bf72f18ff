export default {
    title: '我的订单',
    subtitle: '查看和管理您的订单',
    search: {
        placeholder: '搜索订单号或商品名称',
    },
    loading: '加载中...',
    orderNo: '订单号',
    orderInfo: '订单信息',
    progressAmount: '进度与金额',
    quantity: '数量',
    types: '种商品',
    buyerMessage: '买家留言',
    totalQuantity: '商品总数',
    productTypes: '商品种类',
    orderStatus: '订单状态',
    completion: '完成度',
    quantityLabel: '数量：',
    quantityValue: '{quantity} 件 · {types} 种',
    timeLabel: '时间：',
    buyerMessageLabel: '留言：',
    progressTitle: '订单进度 {percentage}%',
    lastUpdated: '更新：',
    previewModalTitle: '商品预览',
    empty: {
        title: '暂无订单',
        description: '您还没有任何订单，去逛逛商城吧！',
        browsing: '浏览商品',
    },
    status: {
        all: '全部',
        pending: '待支付',
        paid: '已支付',
        purchasing: '采购中',
        shipped: '已发货',
        delivered: '已送达',
        inWarehouse: '已入库',
        completed: '已完成',
        cancelled: '已取消',
        label: '状态',
    },
    product: {
        label: '商品',
    },
    purchaseInfo: {
        label: '采购信息',
    },
    amount: {
        label: '金额',
    },
    createTime: {
        label: '下单时间',
    },
    items: {
        title: '订单商品',
    },
    actions: {
        label: '操作',
        viewDetail: '详情',
        viewTracking: '查看物流',
        pay: '立即支付',
        confirmReceipt: '确认收货',
        cancel: '取消订单',
        refund: '申请退款',
        more: '更多',
        preview: '预览',
    },
    error: {
        loadFailed: '加载订单列表失败',
        confirmFailed: '确认收货失败',
        cancelFailed: '取消订单失败',
    },
    confirm: {
        title: '确认操作',
        receipt: '您确定已经收到商品了吗？',
    },
    cancel: {
        title: '取消订单',
        reason: '请输入取消原因',
        placeholder: '输入取消原因（选填）',
    },
    success: {
        confirmed: '确认收货成功',
        cancelled: '订单取消成功',
    },
    preview: {
        productTitle: '商品标题',
        specs: '规格',
        quantity: '数量',
        unitPrice: '单价',
        loadError: '无法加载商品信息。',
    },
} 