export default {
    title: '结算',
    steps: {
        shipping: '收货地址',
        payment: '支付方式',
        review: '订单确认'
    },
    underDevelopment: '结算功能正在开发中',
    comingSoon: '我们正在努力为您提供流畅的结算体验。此功能即将推出。',
    backToCart: '返回购物车',
    orderPreviewMode: '订单预览模式',
    orderPreviewDescription: '正在处理您的立即购买订单',
    shippingAddress: '收货地址',
    addAddress: '添加新地址',
    useThisAddress: '使用此地址',
    orderNotes: '订单备注',
    fullName: '收件人姓名',
    phoneNumber: '联系电话',
    address: '详细地址',
    city: '城市',
    state: '省/自治区',
    zipCode: '邮政编码',
    paymentMethod: '支付方式',
    creditCard: '信用卡',
    bankTransfer: '银行转账',
    orderItems: '订单商品',
    orderSummary: '订单摘要',
    subtotal: '小计',
    shippingFee: '运费',
    tax: '税费',
    serviceFee: '服务费',
    total: '总计',
    totalQuantity: '总数量',
    totalAmount: '总金额',
    quantity: '数量',
    unitPrice: '单价',
    placeOrder: '提交订单',
    submitOrder: '提交订单',
    orderPlaced: '订单提交成功',
    orderFailed: '订单提交失败，请重试',
    termsAgreement: '我同意',
    termsAndConditions: '服务条款',
    completeAllFields: '请填写所有必填字段',
    noItemsSelected: '没有选择商品进行结账',
    invalidAccessTitle: '无法处理订单',
    invalidAccessMessage: '请通过购物车结算或通过"立即购买"添加您需要购买的商品列表。',
    previewValid: '预览有效',
    previewExpired: '预览已过期',
    remainingTime: '剩余时间',
    expired: '已过期',
    pleaseRefreshPreview: '请刷新预览',
    expiredMessage: '订单预览已过期，请重新进行预览以获取最新价格和库存信息',
    refreshPreview: '刷新预览',
    previewDataExpired: '订单预览数据已过期，将使用购物车数据',
    previewDataError: '获取订单预览失败',
    tokenExpired: '订单预览已过期，请重新预览',
    orderNotePlaceholder: '请输入您的特殊要求或备注信息'
}