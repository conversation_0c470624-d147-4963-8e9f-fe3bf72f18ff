import Home from '@/views/HomeView.vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
// 使用动态导入
// import Search from '@/views/products/SearchView.vue'
import { useUserStore } from '@/stores/user'
import About from '@/views/AboutView.vue'
import NotFound from '@/views/NotFoundView.vue'
import EmailVerificationSuccessView from '@/views/auth/EmailVerificationSuccessView.vue'
import Login from '@/views/auth/LoginView.vue'

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        name: 'home',
        alias: ['/home', '/index'],
        component: Home,
        meta: {
            title: 'title_home',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/login',
        name: 'login',
        component: Login,
        meta: {
            title: 'title_login',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/register',
        name: 'register',
        component: () => import('@/views/auth/RegisterView.vue'),
        meta: {
            title: 'title_register',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/register/success',
        name: 'register-success',
        component: () => import('@/views/auth/RegisterSuccessView.vue'),
        meta: {
            title: 'title_registerSuccess',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/auth/email/verification/:uid',
        name: 'email-verification',
        component: EmailVerificationSuccessView,
        props: true,
        meta: {
            title: 'title_email_verification',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        // 商品详情页
        path: '/products/:offerId',
        name: 'product-detail',
        component: () => import('@/views/products/DetailView.vue'),
        props: true,
        meta: {
            title: 'title_product_details',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        // 更新搜索页路由
        path: '/products/search',
        name: 'search',
        component: () => import('@/views/products/SearchView.vue'),
        props: route => ({
            keyword: route.query.keyword?.toString(),
            categoryId: route.query.categoryId?.toString(),
        }),
        meta: {
            title: 'title_search',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        // 重定向带参数路径格式到查询参数格式
        path: '/products/search/:keyword',
        redirect: to => {
            return {
                path: '/products/search',
                query: {
                    ...to.query,
                    keyword: to.params.keyword,
                },
            }
        },
    },
    {
        path: '/about',
        name: 'about',
        component: About,
        meta: {
            title: 'title_about',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/commission',
        name: 'commission',
        component: () => import('@/views/CommissionView.vue'),
        meta: {
            title: 'title_commission',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/faqs',
        name: 'faqs',
        component: () => import('@/views/FaqsView.vue'),
        meta: {
            title: 'title_faqs',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/services',
        name: 'services',
        component: () => import('@/views/ServicesView.vue'),
        meta: {
            title: 'title_services',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/guide',
        name: 'guide',
        component: () => import('@/views/GuideView.vue'),
        meta: {
            title: 'title_guide',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/user/track-orders',
        name: 'track-orders',
        component: () => import('@/views/user/TrackOrdersView.vue'),
        meta: {
            title: 'title_track_orders',
            requiresAuth: true,
            layout: 'default',
        },
    },
    {
        path: '/user/profile',
        name: 'user-profile',
        component: () => import('@/views/user/UserProfileView.vue'),
        meta: {
            title: 'title_user_profile',
            requiresAuth: true,
            layout: 'default',
        },
    },
    {
        path: '/user/shipping-calculation',
        name: 'shipping-calculation',
        component: () => import('@/views/user/ShippingCalculationView.vue'),
        meta: {
            title: 'title_shipping_calculation',
            requiresAuth: true,
            layout: 'default',
        },
    },
    {
        path: '/user/affiliate',
        name: 'affiliate',
        component: () => import('@/views/user/AffiliateView.vue'),
        meta: {
            title: 'title_affiliate',
            requiresAuth: true,
            layout: 'default',
        },
    },
    {
        path: '/user/browse-history',
        name: 'browse-history',
        component: () => import('@/views/user/BrowseHistoryView.vue'),
        meta: {
            title: 'title_history',
            requiresAuth: true,
            layout: 'default',
        },
    },
    {
        path: '/cart',
        name: 'cart',
        component: () => import('@/views/cart/CartView.vue'),
        meta: {
            title: 'title_cart',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/checkout',
        name: 'checkout',
        component: () => import('@/views/cart/CheckoutView.vue'),
        meta: {
            title: 'title_checkout',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/order-success',
        name: 'order-success',
        component: () => import('@/views/cart/OrderSuccessView.vue'),
        meta: {
            title: 'title_orderSuccess',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/reset-password',
        name: 'reset-password',
        component: () => import('@/views/auth/ResetPasswordView.vue'),
        meta: {
            title: 'title_reset_password',
            requiresAuth: false,
            layout: 'default',
        },
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'not-found',
        component: NotFound,
        meta: {
            title: 'title_404',
            requiresAuth: false,
            layout: 'blank',
        },
    },
]

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes,
    // 添加scrollBehavior配置，控制路由跳转时的滚动行为
    scrollBehavior(to, from, savedPosition) {
        // 始终滚动到顶部
        return { top: 0 }
    },
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
    // 动态导入 i18n 实例以获取当前翻译
    const i18n = (await import('@/i18n')).default
    // 从路由元信息中获取页面标题，如果不存在则使用默认标题
    const pageTitle = to.meta.title ? i18n.global.t(to.meta.title as string) : i18n.global.t('title_default')
    // 设置浏览器标签页标题
    document.title = `${pageTitle} | Fulfillmen Shop`

    console.log(i18n.global.t('router'), `${from.path} -> ${to.path}`)

    const isAuthenticated = useUserStore().isLoggedIn;

    // 如果用户已登录，则禁止访问登录和注册页
    if (isAuthenticated && (to.name === 'login' || to.name === 'register')) {
        return next({ name: 'home' })
    }

    // 如果路由需要授权
    if (to.matched.some(record => record.meta.requiresAuth)) {
        if (!isAuthenticated) {
            // 如果未授权，则重定向到登录页
            return next({
                path: '/login',
                query: { redirect: to.fullPath }
            })
        }
    }

    // 其他情况正常放行
    next()
})

// 全局错误处理
router.onError(error => {
    console.error('路由错误:', error)
})

export default router
