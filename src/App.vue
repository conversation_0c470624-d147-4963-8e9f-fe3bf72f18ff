<template>
    <component :is="currentLayout">
        <!-- 将Transition移到组件外部，确保Suspense有单一根节点 -->
        <Transition name="fade" mode="out-in">
            <Suspense>
                <template #default>
                    <router-view v-slot="{ Component }">
                        <component :is="Component" :key="route.path + JSON.stringify(route.query)" />
                    </router-view>
                </template>
                <template #fallback>
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <p class="loading-text">加载中...</p>
                    </div>
                </template>
            </Suspense>
        </Transition>
    </component>

    <!-- Stagewise Toolbar - 仅在开发模式下显示 -->
    <StagewiseToolbar v-if="isDevelopment" :config="stagewise" />
</template>

<script setup lang="ts">
    import AuthLayout from '@/layouts/AuthLayout.vue'
import BlankLayout from '@/layouts/BlankLayout.vue'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import type { Component } from 'vue'
import { computed, ref, shallowRef } from 'vue'
import { useRoute } from 'vue-router'

    const route = useRoute()

    // 检查是否为开发模式
    const isDevelopment = import.meta.env.DEV

    // Stagewise 相关 - 仅在开发模式下动态导入
    const StagewiseToolbar = shallowRef<Component | null>(null)
    const stagewise = ref<{ plugins: any[] }>({
        plugins: [],
    })

    // 动态导入 Stagewise 依赖（仅在开发模式下）
    if (isDevelopment) {
        Promise.all([
            import('@stagewise-plugins/vue'),
            import('@stagewise/toolbar-vue')
        ]).then(([vuePlugin, toolbarVue]) => {
            StagewiseToolbar.value = toolbarVue.StagewiseToolbar as Component
            stagewise.value = {
                plugins: [vuePlugin.VuePlugin],
            }
        }).catch(error => {
            console.warn('Failed to load Stagewise dependencies:', error)
        })
    }

    // 注册所有可用的布局
    const layouts = {
        default: DefaultLayout,
        blank: BlankLayout,
        auth: AuthLayout,
    }

    // 根据路由元数据动态选择布局
    const currentLayout = computed(() => {
        const layoutName = route.meta.layout || 'default'
        return layouts[layoutName as keyof typeof layouts]
    })
</script>

<style lang="css">
    /* 全局样式 */
    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'Inter', sans-serif;
        background-color: #f5f5f5;
        color: #333;
    }

    /* 过渡动画 */
    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }

    /* 加载状态样式 */
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background-color: #f8f9fa;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    .loading-text {
        font-size: 18px;
        color: #666;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
</style>
