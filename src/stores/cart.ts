// 购物车Store
import { shoppingCartApi, type GetShoppingCartRes } from '@/api/modules/cart'
import i18n from '@/i18n'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash'
import { defineStore } from 'pinia'
import { useUserStore } from './user'

// 在Store中使用t函数的辅助方法
const t = (key: string, values?: any) => i18n.global.t(key, values)

// 定义商品规格接口
export interface ProductSpecs {
    /** 中文规格 */
    attr: {
        attrKey: string
        attrValue: string
    }[]
    /** 英文规格 */
    attrTransEn: {
        attrKey: string
        attrValue: string
    }[]
}

// 定义单位信息接口
export interface UnitInfo {
    /** 中文单位 */
    unit: string
    /** 英文单位 */
    transUnit: string
}

// 定义分类信息接口
export interface CategoryInfo {
    /** 分类ID */
    categoryId: string
    /** 分类名称（中文） */
    categoryName: string
    /** 分类名称（英文） */
    categoryNameTrans?: string
}

// 定义店铺信息接口
export interface ShopInfo {
    /** 店铺ID */
    shopId: string
    /** 店铺名称 */
    shopName: string
}

// 购物车项接口
export interface CartItem {
    /** 购物车项ID - 统一使用string类型 */
    id: string
    /** 购物车数据库ID - 用于后端操作 */
    cartId: number
    /** 商品ID - 统一使用string类型 */
    productId: string
    /** 平台产品ID - 用于产品详情查询 */
    platformProductId?: string
    /** SKU ID - 统一使用string类型 */
    skuId: string
    /** 商品标题（中文） */
    name: string
    /** 商品标题（英文翻译） */
    nameTranslated?: string
    /** 商品主图URL */
    image: string
    /** 商品单价（分） - 使用分为单位避免浮点数精度问题 */
    price: number
    /** 商品原价/美元价格（分） */
    originalPrice?: number
    /** 购买数量 */
    quantity: number
    /** 最小起订量 */
    minOrderQuantity?: number
    /** 是否选中 */
    selected: boolean
    /** 添加时间戳 */
    timestamp: number
    /** 是否为无SKU商品 */
    isNoSkuProduct: boolean
    /** 单位信息 */
    unitInfo: UnitInfo
    /** 商品规格信息 */
    specs: ProductSpecs
    /** 店铺信息 */
    shopInfo?: ShopInfo
}

// 购物车状态接口
export interface CartState {
    /** 购物车商品列表 */
    items: CartItem[]
    /** 抽屉是否打开 */
    isDrawerOpen: boolean
    /** 加载状态 */
    loading: boolean
    /** 同步状态 */
    syncing: boolean
    /** 最后更新时间 */
    lastUpdated: number
    /** 是否为无SKU商品 */
    isNoSkuProduct?: boolean
    /** 单位信息 */
    unitInfo?: UnitInfo
    /** 商品规格信息 */
    specs?: ProductSpecs
    /** 店铺信息 */
    shopInfo?: ShopInfo
}

// 添加到购物车参数接口
export interface AddToCartParams {
    productId: string
    skuId: string
    quantity: number
    name?: string
    nameTranslated?: string
    price?: number
    originalPrice?: number
    image?: string
    isNoSkuProduct?: boolean
    unitInfo?: UnitInfo
    specs?: ProductSpecs
    shopInfo?: ShopInfo
}

// 批量添加SKU参数接口
export interface BatchAddSkuParams {
    productId: string
    skuList: Array<{
        skuId: string
        quantity: number
        specId?: string
        [key: string]: any
    }>
    productInfo: {
        name: string
        nameTranslated?: string
        image: string
        price?: number
        originalPrice?: number
        isNoSkuProduct?: boolean
        unitInfo?: UnitInfo
        specs?: ProductSpecs
        shopInfo?: ShopInfo
    }
}

// 价格工具类
export class PriceUtils {
    // 元转分
    static yuanToCents(yuan: number): number {
        return Math.round(yuan * 100)
    }

    // 分转元
    static centsToYuan(cents: number): number {
        return cents / 100
    }

    // 计算总价（分）
    static calculateTotal(price: number, quantity: number): number {
        return price * quantity
    }
}

// 购物车数据转换器
export class CartDataTransformer {
    /**
     * 将服务器返回的购物车数据转换为前端购物车项
     */
    static transformServerItem(item: GetShoppingCartRes, selectedState: Record<string, boolean> = {}): CartItem {
        const itemKey = `${item.productId}:${item.skuId}`
        // 优先使用服务器返回的选中状态，其次使用传入的状态，最后默认为true
        let isSelected = true
        if (item.isChecked !== undefined) {
            isSelected = item.isChecked === 1
        } else if (selectedState[itemKey] !== undefined) {
            isSelected = selectedState[itemKey]
        }

        // 价格处理：保存原始的人民币和美元价格
        const priceCents = PriceUtils.yuanToCents(item.price || 0)
        const usdPriceCents = item.usdPrice ? PriceUtils.yuanToCents(item.usdPrice) : undefined

        // 确保规格字段符合 ProductSpecs 结构
        let specs: ProductSpecs = { attr: [], attrTransEn: [] }
        if (item.specs && Array.isArray(item.specs) && item.specs.length > 0) {
            const specArray = item.specs as any[]
            specs.attr = specArray.map(s => ({ attrKey: s.attrKey, attrValue: s.attrValue }))
            specs.attrTransEn = specArray.map(s => ({
                attrKey: s.attrKeyTrans || s.attrKey,
                attrValue: s.attrValueTrans || s.attrValue,
            }))
        } else if (item.skuId) {
            // 降级处理，当没有规格信息时
            specs = { attr: [], attrTransEn: [] }
        }

        return {
            id: `${item.productId}_${item.skuId}_${Date.now()}`,
            cartId: item.id,
            productId: String(item.productId),
            platformProductId: item.platformProductId || String(item.productId),
            skuId: String(item.skuId),
            name: item.title || '',
            nameTranslated: item.titleTrans || undefined,
            image: item.mainImgUrl || '',
            price: priceCents,
            originalPrice: usdPriceCents,
            quantity: item.quantity || 1,
            minOrderQuantity: item.minOrderQuantity,
            selected: isSelected,
            timestamp: Date.now(),
            isNoSkuProduct: false,
            unitInfo: { unit: '件', transUnit: 'pcs' },
            specs: specs,
            shopInfo: {
                shopId: item.sellerOpenId || '',
                shopName: item.sellerName || '未知店铺',
            },
        }
    }

    /**
     * 将服务器购物车数据转换为购物车项数组
     */
    static transformFromServer(serverData: GetShoppingCartRes[]): CartItem[] {
        if (!Array.isArray(serverData)) {
            return []
        }
        return serverData.map(item => this.transformServerItem(item))
    }

    /**
     * 根据添加购物车参数创建购物车项
     */
    static createFromParams(params: AddToCartParams): CartItem {
        return {
            id: `${params.productId}_${params.skuId}_${Date.now()}`,
            cartId: 0,
            productId: params.productId,
            platformProductId: params.productId,
            skuId: params.skuId,
            name: params.name || '',
            nameTranslated: params.nameTranslated,
            image: params.image || '',
            price: params.price || 0,
            originalPrice: params.originalPrice,
            quantity: params.quantity,
            selected: true,
            timestamp: Date.now(),
            isNoSkuProduct: params.isNoSkuProduct || false,
            unitInfo: params.unitInfo || { unit: '件', transUnit: 'pcs' },
            specs: params.specs || { attr: [], attrTransEn: [] },
            shopInfo: params.shopInfo,
        }
    }

    /**
     * 根据批量添加参数创建购物车项数组
     */
    static createFromBatchParams(params: BatchAddSkuParams): CartItem[] {
        return params.skuList
            .filter(sku => sku.quantity > 0)
            .map(sku => ({
                id: `${params.productId}_${sku.skuId}_${Date.now()}`,
                cartId: 0,
                productId: params.productId,
                platformProductId: params.productId,
                skuId: sku.skuId,
                name: params.productInfo.name,
                nameTranslated: params.productInfo.nameTranslated,
                image: params.productInfo.image,
                price: params.productInfo.price || 0,
                originalPrice: params.productInfo.originalPrice,
                quantity: sku.quantity,
                selected: true,
                timestamp: Date.now(),
                isNoSkuProduct: params.productInfo.isNoSkuProduct || false,
                unitInfo: params.productInfo.unitInfo || { unit: '件', transUnit: 'pcs' },
                specs: sku.specs || params.productInfo.specs || { attr: [], attrTransEn: [] },
                shopInfo: params.productInfo.shopInfo,
            }))
    }
}

/**
 * 购物车Store
 * 提供购物车数据存储和操作方法
 * 要求用户登录后才能使用购物车功能
 */
export const useCartStore = defineStore('cart', {
    state: (): CartState => ({
        items: [],
        isDrawerOpen: false,
        loading: false,
        syncing: false,
        lastUpdated: 0,
    }),

    getters: {
        // 购物车总数量
        cartCount: state => (state.items || []).reduce((sum, item) => sum + item.quantity, 0),

        // 计算选中商品总金额（分）
        totalAmountCents: state => {
            return (state.items || []).filter(item => item.selected).reduce((sum, item) => sum + PriceUtils.calculateTotal(item.price, item.quantity), 0)
        },

        // 计算选中商品总金额（元，保持两位小数）
        totalAmount: state => {
            const totalCents = (state.items || []).filter(item => item.selected).reduce((sum, item) => sum + PriceUtils.calculateTotal(item.price, item.quantity), 0)
            return PriceUtils.centsToYuan(totalCents)
        },

        // 选中的商品数量
        selectedCount: state => (state.items || []).filter(item => item.selected).length,

        // 是否全选
        isAllSelected: state => {
            const items = state.items || []
            return items.length > 0 && items.every(item => item.selected)
        },

        // 按商品ID分组购物车商品
        groupedByProduct(
            state: CartState
        ): Record<string, { productName: string; productNameEn: string; items: CartItem[] }> {
            const grouped: Record<string, { productName: string; productNameEn: string; items: CartItem[] }> = {}

            for (const item of state.items) {
                const productId = item.productId
                if (!grouped[productId]) {
                    grouped[productId] = {
                        productName: item.name,
                        productNameEn: item.nameTranslated || item.name,
                        items: [],
                    }
                }
                grouped[productId].items.push(item)
            }
            return grouped
        },

        // 获取选中的商品
        selectedItems: state => (state.items || []).filter(item => item.selected),

        // 购物车是否为空
        isEmpty: state => (state.items || []).length === 0,
    },

    actions: {
        // 设置加载状态
        setLoading(loading: boolean) {
            this.loading = loading
        },

        // 更新最后更新时间
        updateLastUpdated() {
            this.lastUpdated = Date.now()
        },

        // 检查用户登录状态
        checkUserLogin(): boolean {
            const userStore = useUserStore()
            if (!userStore.isLoggedIn) {
                ElMessage.warning(t('cart.loginRequired'))
                return false
            }
            return true
        },

        /**
         * 从服务器获取购物车数据
         */
        async fetchFromServer() {
            try {
                const userStore = useUserStore()
                if(!userStore.isLoggedIn){
                    console.log("未登录用户，跳过获取购物车")
                    return;
                }
                console.log('📦 开始从服务器获取购物车数据...')
                const response = await shoppingCartApi.getCartList()
                console.log('✅ 服务器返回购物车数据:', {
                    total: response.length,
                    items: response.map(item => ({
                        id: item.id,
                        productId: item.productId,
                        title: item.title,
                        quantity: item.quantity,
                        price: item.price,
                        usdPrice: item.usdPrice,
                        seller: item.sellerName
                    }))
                })

                // 转换数据并保持选择状态
                const currentSelectionState: Record<string, boolean> = {}
                this.items.forEach(item => {
                    const key = `${item.productId}:${item.skuId}`
                    currentSelectionState[key] = item.selected
                })

                this.items = CartDataTransformer.transformFromServer(response)
                console.log('🔄 数据转换完成，购物车商品数量:', this.items.length)

                // 恢复选择状态
                this.items.forEach(item => {
                    const key = `${item.productId}:${item.skuId}`
                    if (currentSelectionState[key] !== undefined) {
                        item.selected = currentSelectionState[key]
                    }
                })
            } catch (error) {
                console.error('❌ 从服务器获取购物车失败:', error)
                throw error
            }
        },

        /**
         * 获取购物车列表（仅支持已登录用户）
         */
        async fetchCartList() {
            try {
                if (!this.checkUserLogin()) {
                    this.items = []
                    return []
                }

                this.setLoading(true)
                await this.fetchFromServer()
                this.updateLastUpdated()
                return this.items
            } catch (error) {
                console.error('获取购物车失败:', error)
                ElMessage.error(t('cart.fetchFailed'))
                throw error
            } finally {
                this.setLoading(false)
            }
        },

        /**
         * 添加商品到购物车
         */
        async addProductToCart(params: AddToCartParams) {
            try {
                if (!this.checkUserLogin()) {
                    return
                }

                console.log('🛒 添加商品到购物车:', {
                    productId: params.productId,
                    skuId: params.skuId,
                    quantity: params.quantity,
                    name: params.name
                })

                await shoppingCartApi.addProductToCart({
                    productId: params.productId,
                    purchaseSkuInfos: [{
                        skuId: params.skuId,
                        qty: params.quantity
                    }],
                })

                console.log('✅ 商品添加成功，刷新购物车数据...')
                await this.fetchCartList()
                ElMessage.success(t('cart.addSuccess'))
            } catch (error) {
                console.error('❌ 添加购物车失败:', error)
                ElMessage.error(t('cart.addFailed'))
                throw error
            }
        },

        /**
         * 批量添加多个SKU到购物车
         */
        async addMultipleSkusToCart(params: BatchAddSkuParams) {
            try {
                if (!this.checkUserLogin()) {
                    return
                }

                // 过滤掉数量为0的SKU
                const validSkus = params.skuList.filter(sku => sku.quantity > 0)

                if (validSkus.length === 0) {
                    return
                }

                await shoppingCartApi.addProductToCart({
                    productId: params.productId,
                    purchaseSkuInfos: validSkus.map(sku => ({
                        skuId: sku.skuId,
                        qty: sku.quantity,
                        ...(sku.specId ? { specId: sku.specId } : {})
                    })),
                })

                await this.fetchCartList()

                const totalItems = validSkus.reduce((sum, sku) => sum + sku.quantity, 0)
                ElMessage.success(t('cart.addMultipleSuccess', { count: totalItems }))
            } catch (error) {
                console.error('批量添加购物车失败:', error)
                const errorMessage = error instanceof Error ? error.message : '批量添加购物车失败'
                ElMessage.error(errorMessage)
                throw error
            }
        },

        /**
         * 添加多个SKU到购物车 (别名方法)
         * 为了兼容 ProductOptions.vue 中的调用
         */
        async addSkusToCart(productId: string, skuInfos: any[], productInfo: any) {
            const params: BatchAddSkuParams = {
                productId,
                skuList: skuInfos.map(sku => ({
                    skuId: sku.skuId || 'default',
                    quantity: sku.quantity || 1,
                    specId: sku.specId,
                    specs: sku.specs,
                })),
                productInfo: {
                    name: productInfo.name || '',
                    nameTranslated: productInfo.nameTranslated,
                    image: productInfo.image || '',
                    price: productInfo.price,
                    originalPrice: productInfo.originalPrice,
                    isNoSkuProduct: productInfo.isNoSkuProduct || false,
                    unitInfo: productInfo.unitInfo || { unit: '件', transUnit: 'pcs' },
                    specs: productInfo.specs || { attr: [], attrTransEn: [] },
                    shopInfo: productInfo.shopInfo
                }
            }

            return await this.addMultipleSkusToCart(params)
        },

        /**
         * 更新购物车商品数量
         */
        async updateQuantity(productId: string, skuId: string, quantity: number) {
            try {
                if (!this.checkUserLogin()) {
                    return
                }

                await shoppingCartApi.updateCartProductQuantity(productId, skuId, quantity)
                await this.fetchCartList()
                this.updateLastUpdated()
            } catch (error) {
                console.error('更新购物车数量失败:', error)
                ElMessage.error(t('cart.updateFailed'))
            }
        },

        /**
         * 从购物车删除商品
         */
        async removeCartItem(productId: string, skuIds: string[] | string) {
            try {
                if (!this.checkUserLogin()) {
                    return
                }

                // 确保 skuIds 是数组类型
                const skuIdsArray = Array.isArray(skuIds) ? skuIds : skuIds ? [skuIds] : []
                // 是否删除整个产品组（使用空数组判断）
                const isRemoveProduct = skuIdsArray.length === 0

                if (isRemoveProduct) {
                    await shoppingCartApi.deleteCartProductByProductId(productId)
                } else {
                    await shoppingCartApi.deleteCartProduct(productId, skuIdsArray)
                }

                await this.fetchCartList()
                this.updateLastUpdated()
                ElMessage.success(t('cart.removeSuccess'))
            } catch (error) {
                console.error('删除购物车商品失败:', error)
                ElMessage.error(t('cart.removeFailed'))
            }
        },

        /**
         * 清空购物车
         */
        async clearCart() {
            try {
                if (!this.checkUserLogin()) {
                    return
                }

                await shoppingCartApi.clearCart()
                await this.fetchCartList()
                this.updateLastUpdated()
                ElMessage.success(t('cart.clearSuccess'))
            } catch (error) {
                console.error('清空购物车失败:', error)
                ElMessage.error(t('cart.clearFailed'))
            }
        },

        /**
         * 移除选中的商品
         */
        removeSelectedItems() {
            // 获取选中的商品，按productId分组
            const selectedItemsByProduct = new Map<string, string[]>()

            this.items.forEach(item => {
                if (item.selected) {
                    if (!selectedItemsByProduct.has(item.productId)) {
                        selectedItemsByProduct.set(item.productId, [])
                    }
                    selectedItemsByProduct.get(item.productId)?.push(item.skuId)
                }
            })

            // 批量删除
            const deletePromises = Array.from(selectedItemsByProduct.entries()).map(([productId, skuIds]) =>
                this.removeCartItem(productId, skuIds)
            )

            Promise.all(deletePromises).catch(error => {
                console.error('批量删除失败:', error)
            })
        },

        /**
         * 根据ID移除商品
         */
        removeItem(itemId: string) {
            try {
                const item = this.items.find(item => item.id === itemId)
                if (item) {
                    this.removeCartItem(item.productId, [item.skuId])
                }
            } catch (error) {
                console.error('移除商品失败:', error)
                ElMessage.error(t('cart.removeFailed'))
            }
        },

        /**
         * 切换抽屉状态
         */
        toggleDrawer(isOpen?: boolean) {
            this.isDrawerOpen = isOpen !== undefined ? isOpen : !this.isDrawerOpen
        },

        /**
         * 切换单个商品选择状态
         * 仅更新本地状态，并触发防抖的后端同步
         */
        toggleSelection(itemId: string) {
            const item = this.items.find(item => item.id === itemId)
            if (item) {
                item.selected = !item.selected
                // 调用防抖的同步方法，无需等待
                this.updateSelectionState()
            }
        },

        /**
         * 切换全选/取消全选
         */
        async toggleAllSelection(selected: boolean) {
            try {
                // 先更新前端状态
                this.items.forEach(item => {
                    item.selected = selected
                })

                // 同步到后端
                if (this.checkUserLogin() && this.items.length > 0) {
                    // 提取有效的购物车ID列表（排除cartId为0的本地项）
                    const cartIds = this.items
                        .filter(item => item.cartId > 0)
                        .map(item => item.cartId)

                    if (cartIds.length > 0) {
                        // 传入选中状态参数
                        await shoppingCartApi.batchUpdateItemCheckedStatus(cartIds, selected ? 1 : 0)
                    }
                }

                this.updateLastUpdated()
            } catch (error) {
                console.error('切换全选状态失败:', error)
                // 如果后端同步失败，回滚前端状态
                this.items.forEach(item => {
                    item.selected = !selected
                })
                ElMessage.error('更新全选状态失败')
            }
        },

        /**
         * 更新购物车选择状态（带防抖）
         * 将所有当前的选择状态批量同步到后端，最多发起两次请求（一次选中，一次取消选中）
         */
        updateSelectionState: debounce(async function (this: CartState & { checkUserLogin: () => boolean; updateLastUpdated: () => void }) {
            try {
                if (!this.checkUserLogin()) {
                    return
                }

                const itemsToSync = this.items.filter((item: CartItem) => item.cartId > 0)
                if (itemsToSync.length === 0) {
                    return
                }

                console.log('🔄 [DEBOUNCED] 开始批量同步选择状态...', itemsToSync.length, '个商品')

                const checkedIds = itemsToSync.filter(item => item.selected).map(item => item.cartId)
                const uncheckedIds = itemsToSync.filter(item => !item.selected).map(item => item.cartId)

                const promises = []

                if (checkedIds.length > 0) {
                    promises.push(shoppingCartApi.batchUpdateItemCheckedStatus(checkedIds, 1))
                }

                if (uncheckedIds.length > 0) {
                    promises.push(shoppingCartApi.batchUpdateItemCheckedStatus(uncheckedIds, 0))
                }

                if (promises.length > 0) {
                    await Promise.allSettled(promises)
                    console.log('✅ [DEBOUNCED] 选择状态同步完成')
                }

                this.updateLastUpdated()
            } catch (error) {
                console.error('批量更新选择状态失败:', error)
                ElMessage.error('同步购物车状态失败')
            }
        }, 500), // 500ms 防抖延迟

        /**
         * 获取显示价格（元）
         */
        getDisplayPrice(item: CartItem): number {
            return PriceUtils.centsToYuan(item.price)
        },

        /**
         * 获取显示原价（元）
         */
        getDisplayOriginalPrice(item: CartItem): number | undefined {
            return item.originalPrice ? PriceUtils.centsToYuan(item.originalPrice) : undefined
        },


    },
})

// 购物车同步钩子函数（现在只需要在登录后刷新购物车）
export function setupCartSyncHooks() {
    const cartStore = useCartStore()
    const userStore = useUserStore()

    // 监听登录状态变化
    userStore.$subscribe((mutation, state) => {
        if (state.isLoggedIn) {
            // 用户登录后刷新购物车
            cartStore.fetchCartList().catch(error => {
                console.error('登录后获取购物车失败:', error)
            })
        } else {
            // 用户登出后清空购物车
            cartStore.items = []
        }
    })
}
