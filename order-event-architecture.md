# 订单事件处理架构设计

## 概述

基于您的需求，我设计了一个统一的订单事件处理架构，实现了"上层调用只需要创建对应的事件对象，event包负责实现对应业务逻辑"的设计理念。

## 核心设计原则

### 1. 事件驱动架构
- 所有订单相关的业务逻辑都通过事件触发
- 事件发布是同步的，事件处理是异步的
- 事件之间解耦，便于扩展和维护

### 2. 职责分离
- **上层业务**：只负责调用 `OrderEventPublisher` 发布事件
- **Event包**：负责所有具体的业务逻辑实现
- **监听器**：处理特定类型的事件，执行相应的业务操作

### 3. 统一接口
- 提供 `OrderEventPublisher` 作为统一的事件发布入口
- 简化上层调用，隐藏事件处理的复杂性
- 支持事件组合和批量处理

## 架构组件

### 核心组件

#### 1. OrderEventPublisher（统一事件发布器）
```java
@Component
public class OrderEventPublisher {
    // 订单生命周期事件
    void publishOrderCreatedEvent(OrderContext orderContext);
    void publishOrderPaidEvent(Long orderId, BigDecimal amount, String method, String transactionId);
    void publishOrderCancelledEvent(Long orderId, String reason, String cancelledBy);
    void publishOrderCompletedEvent(Long orderId, String note);
    
    // 采购相关事件
    void publishProcurementStartedEvent(Long orderId, String supplierInfo);
    void publishProcurementCompletedEvent(Long orderId, String externalOrderId, String data);
    void publishProcurementFailedEvent(Long orderId, String reason, String errorDetails);
    
    // 物流相关事件
    void publishGoodsShippedEvent(Long orderId, String trackingNumber, String carrier);
    void publishGoodsArrivedWarehouseEvent(Long orderId, String warehouseId, Integer quantity);
    void publishGoodsStockedEvent(Long orderId, Integer quantity, String qualityResult);
    
    // 异常处理事件
    void publishOrderExceptionEvent(Long orderId, String type, String message, String data);
    
    // 批量事件发布
    void publishOrderStatusFlowEvent(Long orderId, OrderStatusFlow statusFlow);
    void publishOrderEventGroup(OrderEventGroup eventGroup);
}
```

#### 2. 事件类型枚举（OrderEventTypeEnums）
```java
public enum OrderEventTypeEnums {
    // 订单生命周期事件
    ORDER_CREATED("订单创建"),
    ORDER_PAID("订单支付"),
    ORDER_CANCELLED("订单取消"),
    ORDER_COMPLETED("订单完成"),
    
    // 采购相关事件
    PROCUREMENT_STARTED("采购开始"),
    PROCUREMENT_COMPLETED("采购完成"),
    PROCUREMENT_FAILED("采购失败"),
    
    // 物流相关事件
    GOODS_SHIPPED("商品发货"),
    GOODS_ARRIVED_WAREHOUSE("商品到达仓库"),
    GOODS_STOCKED("商品入库"),
    
    // 异常处理事件
    ORDER_EXCEPTION("订单异常"),
    PAYMENT_EXCEPTION("支付异常"),
    INVENTORY_INSUFFICIENT("库存不足");
}
```

### 事件监听器

#### 1. OrderCreatedEventListener（通用订单创建监听器）
处理订单创建后的通用业务逻辑：
- 记录订单创建日志
- 发送用户通知
- 更新订单统计数据
- 触发相关业务流程
- 集成第三方系统

#### 2. PurchaseOrderCreatedEventListener（采购订单创建监听器）
专门处理采购相关的业务逻辑：
- 调用阿里巴巴API创建供应商订单
- 更新外部订单信息
- 处理多供应商订单
- 异常处理和重试机制

#### 3. OrderStatusChangeEventListener（订单状态变更监听器）
处理订单状态变更相关的业务逻辑：
- 记录状态变更日志
- 发送状态变更通知
- 更新缓存数据
- 同步第三方系统
- 执行业务规则检查

## 使用方式

### 1. 简单事件发布
```java
@Service
public class OrderService {
    
    @Autowired
    private OrderEventPublisher orderEventPublisher;
    
    public void createOrder(OrderContext orderContext) {
        // 创建订单的业务逻辑
        saveOrder(orderContext);
        
        // 发布订单创建事件 - 一行代码搞定！
        orderEventPublisher.publishOrderCreatedEvent(orderContext);
        
        // 事件发布后，相关的业务逻辑会异步执行：
        // - 采购订单创建
        // - 用户通知发送
        // - 订单统计更新
        // - 第三方系统集成
        // 等等...
    }
}
```

### 2. 支付完成流程
```java
public void handlePaymentCompleted(Long orderId, PaymentInfo paymentInfo) {
    // 发布支付完成事件
    orderEventPublisher.publishOrderPaidEvent(
        orderId, 
        paymentInfo.getAmount(), 
        paymentInfo.getMethod(), 
        paymentInfo.getTransactionId()
    );
    
    // 发布采购开始事件
    orderEventPublisher.publishProcurementStartedEvent(
        orderId, 
        "阿里巴巴供应商"
    );
}
```

### 3. 批量事件组合
```java
public void handleComplexBusinessFlow(Long orderId) {
    // 创建事件组合
    OrderEventGroup eventGroup = OrderEventGroup.createOrderLifecycleGroup(
        orderId,
        Arrays.asList(
            new OrderPaidEvent(...),
            new ProcurementStartedEvent(...),
            new GoodsShippedEvent(...)
        ),
        tenantId
    );
    
    // 批量发布事件
    orderEventPublisher.publishOrderEventGroup(eventGroup);
}
```

## 优势特点

### 1. 简化上层调用
- 上层业务只需要一行代码发布事件
- 无需关心具体的业务逻辑实现
- 代码简洁、易于理解和维护

### 2. 业务逻辑解耦
- 每个监听器负责特定的业务逻辑
- 监听器之间相互独立，便于扩展
- 支持多个监听器处理同一个事件

### 3. 异步处理
- 事件处理是异步的，不会阻塞主流程
- 提高系统响应性能
- 支持并发处理

### 4. 扩展性强
- 新增事件类型只需要添加事件类和监听器
- 现有代码无需修改
- 支持事件组合和复杂业务流程

### 5. 监控和调试
- 统一的事件发布入口，便于监控
- 详细的日志记录
- 支持事件追踪和调试

## 实现的具体改动

### 1. 完善了事件类型枚举
- 扩展了 `OrderEventTypeEnums`，增加了更多事件类型
- 添加了事件分类方法（生命周期、采购、异常等）

### 2. 创建了统一的事件发布器
- `OrderEventPublisher` - 提供统一的事件发布接口
- 隐藏了事件创建和发布的复杂性
- 支持各种类型的订单事件

### 3. 设计了完整的事件类体系
- `OrderCreatedEvent` - 订单创建事件
- `OrderPaidEvent` - 订单支付事件
- `OrderCancelledEvent` - 订单取消事件
- `OrderCompletedEvent` - 订单完成事件
- `ProcurementStartedEvent` - 采购开始事件
- `ProcurementCompletedEvent` - 采购完成事件
- `ProcurementFailedEvent` - 采购失败事件
- 以及物流和异常相关事件

### 4. 重构了现有的事件处理逻辑
- 更新了 `OrderEventManager` 使用新的事件发布器
- 完善了 `PurchaseOrderCreatedEventListener` 的事件处理
- 创建了通用的 `OrderCreatedEventListener`

### 5. 添加了高级功能
- `OrderStatusFlow` - 支持复杂的状态流转
- `OrderEventGroup` - 支持事件组合和批量处理
- `OrderEventUsageExample` - 提供详细的使用示例

## 总结

这个架构实现了您要求的设计目标：
- **上层调用简单**：只需要调用 `OrderEventPublisher` 的相应方法
- **业务逻辑分离**：所有具体实现都在 event 包的监听器中
- **易于扩展**：新增业务逻辑只需要添加监听器
- **高性能**：异步处理，不阻塞主流程
- **易于维护**：代码结构清晰，职责明确

现在您的订单处理逻辑变得非常简洁和优雅！