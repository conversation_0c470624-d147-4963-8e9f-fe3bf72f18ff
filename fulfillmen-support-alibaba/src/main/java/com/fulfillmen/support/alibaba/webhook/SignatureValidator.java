/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * HMAC-SHA1 签名验证器
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@Component
public class SignatureValidator {

    @Value("${alibaba.open1688.secret-key:}")
    private String webhookSecret;

    /**
     * 验证HMAC-SHA1签名
     * 
     * @param payload   原始消息内容
     * @param signature 签名（Base64编码）
     * @return 验证结果
     */
    public boolean validate(String payload, String signature) {
        if (webhookSecret == null || webhookSecret.isEmpty()) {
            log.warn("Webhook secret未配置，跳过签名验证");
            // 如果没有配置密钥，则跳过验证
            return true;
        }

        if (signature == null || signature.isEmpty()) {
            log.error("签名为空");
            return false;
        }

        try {
            String expectedSignature = calculateSignature(payload);
            boolean isValid = expectedSignature.equals(signature);

            if (!isValid) {
                log.error("签名验证失败，期望: {}, 实际: {}", expectedSignature, signature);
            }

            return isValid;

        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 计算HMAC-SHA1签名
     */
    private String calculateSignature(String payload) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKeySpec = new SecretKeySpec(
            webhookSecret.getBytes(StandardCharsets.UTF_8),
            "HmacSHA1"
        );
        mac.init(secretKeySpec);

        byte[] signatureBytes = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signatureBytes);
    }
}