/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/9 19:06
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum LogisticsMessageTypeEnums implements CallbackMessageType {

    /**
     * 物流单状态变更（买家视角）
     */
    LOGISTICS_BUYER_VIEW_TRACE("LOGISTICS_BUYER_VIEW_TRACE", "物流单状态变更（买家视角）"),
    /**
     * 物流单号修改消息
     */
    LOGISTICS_MAIL_NO_CHANGE("LOGISTICS_MAIL_NO_CHANGE", "物流单号修改消息"),
    /**
     * 1688跨境物流包裹消息
     */
    LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE("LOGISTICS_GLOBAL_1688_PACKAGE_CHANGE", "1688跨境物流包裹消息"),
    ;

    private final String messageType;
    private final String messageDesc;

    LogisticsMessageTypeEnums(String messageType, String messageDesc) {
        this.messageType = messageType;
        this.messageDesc = messageDesc;
    }

}
