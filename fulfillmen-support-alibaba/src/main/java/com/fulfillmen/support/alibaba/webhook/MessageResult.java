/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Builder;
import lombok.Data;

/**
 * 消息处理结果
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Data
@Builder
public class MessageResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 处理是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理结果数据
     */
    private Object data;

    /**
     * 处理完成时间
     */
    private LocalDateTime processedAt;

    /**
     * 创建成功结果
     */
    public static MessageResult success(String msgId) {
        return MessageResult.builder()
            .msgId(msgId)
            .success(true)
            .processedAt(LocalDateTime.now())
            .build();
    }

    /**
     * 创建成功结果（带数据）
     */
    public static MessageResult success(String msgId, Object data) {
        return MessageResult.builder()
            .msgId(msgId)
            .success(true)
            .data(data)
            .processedAt(LocalDateTime.now())
            .build();
    }

    /**
     * 创建失败结果
     */
    public static MessageResult error(String msgId, String errorMessage) {
        return MessageResult.builder()
            .msgId(msgId)
            .success(false)
            .errorMessage(errorMessage)
            .processedAt(LocalDateTime.now())
            .build();
    }

    /**
     * 创建无处理器结果
     */
    public static MessageResult noHandler(String msgId) {
        return error(msgId, "未找到适合的消息处理器");
    }

    /**
     * 创建重复消息结果
     */
    public static MessageResult duplicate(String msgId) {
        return MessageResult.builder()
            .msgId(msgId)
            .success(true)
            .errorMessage("消息重复，已跳过处理")
            .processedAt(LocalDateTime.now())
            .build();
    }
}