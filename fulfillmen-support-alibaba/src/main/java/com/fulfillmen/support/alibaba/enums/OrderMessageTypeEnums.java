/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 交易消息类型
 *
 * <AUTHOR>
 * @date 2025/7/9 18:57
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum OrderMessageTypeEnums implements CallbackMessageType {

    /**
     * 1688创建订单（买家视角）/order created (buyer view)
     * <pre>
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitbuyerpay",
     * "msgSendTime": "2018-05-30 19: 24: 18",
     * "buyerMemberId": "b2b-665170100",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     *
     * </pre>
     */
    ORDER_BUYER_VIEW_BUYER_MAKE("ORDER_BUYER_VIEW_BUYER_MAKE", "1688创建订单（买家视角）/order created (buyer view)"),
    /**
     * 1688修改订单价格（买家视角）/order price modification (buyer view)
     * <pre>
     * 消息格式:
     * {
     * "orderId": 167539019420540000,
     * "currentStatus": "waitbuyerpay",
     * "msgSendTime": "2018-05-30 19: 24: 18",
     * "buyerMemberId": "b2b-665170100",
     * "sellerMemberId": "b2b-1676547900b7bb3"
     * }
     * </pre>
     */
    ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY("ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY",
        "1688修改订单价格（买家视角）/order price modification (buyer view)"),
    /**
     * 1688交易付款（买家视角）/1688 transaction payment (buyer view)
     */
    ORDER_BUYER_VIEW_ORDER_PAY("ORDER_BUYER_VIEW_ORDER_PAY", "1688交易付款（买家视角）/1688 transaction payment (buyer view)"),
    /**
     * 1688订单发货（买家视角）/1688 order delivery (buyer view)
     */
    ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS("ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS",
        "1688订单发货（买家视角）/1688 order delivery (buyer view)"),
    /**
     * 1688订单部分发货（买家视角）/Partial delivery of 1688 order (buyer view)
     */
    ORDER_BUYER_VIEW_PART_PART_SENDGOODS("ORDER_BUYER_VIEW_PART_PART_SENDGOODS",
        "1688订单部分发货（买家视角）/Partial delivery of 1688 order (buyer view)"),
    /**
     * 1688订单确认收货（买家视角）/order receipt confirmation (buyer view)
     */
    ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS("ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS",
        "1688订单确认收货（买家视角）/order receipt confirmation (buyer view)"),
    /**
     * 1688交易成功（买家视角）
     */
    ORDER_BUYER_VIEW_ORDER_SUCCESS("ORDER_BUYER_VIEW_ORDER_SUCCESS", "1688交易成功（买家视角）"),
    /**
     * 1688买家关闭订单（买家视角）/buyer closing order (buyer view)
     */
    ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE("ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE",
        "1688买家关闭订单（买家视角）/buyer closing order (buyer view)"),
    /**
     * 1688运营后台关闭订单（买家视角）
     */
    ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE("ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE", "1688运营后台关闭订单（买家视角）"),
    /**
     * 1688订单售中退款（买家视角）
     */
    ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES("ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES", "1688订单售中退款（买家视角）"),
    /**
     * 1688订单售后退款（买家视角）
     */
    ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES("ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES", "1688订单售后退款（买家视角）"),
    ;

    private final String messageType;
    private final String messageDesc;

    OrderMessageTypeEnums(String messageType, String messageDesc) {
        this.messageType = messageType;
        this.messageDesc = messageDesc;
    }

}
