/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.fulfillmen.support.alibaba.enums.CallbackMessageType;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象消息处理器
 * 
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractTypedMessageHandler<T> implements MessageHandler<T> {

    private final List<CallbackMessageType> supportedTypes;

    protected AbstractTypedMessageHandler(CallbackMessageType... types) {
        this.supportedTypes = Arrays.asList(types);
    }

    @Override
    public boolean canHandle(CallbackMessageType messageType) {
        return supportedTypes.contains(messageType);
    }

    @Override
    public List<CallbackMessageType> getSupportedTypes() {
        return Collections.unmodifiableList(supportedTypes);
    }

    /**
     * 具体的业务处理逻辑，由子类实现
     */
    protected abstract void doHandle(T data, MessageEvent<T> event);

    @Override
    public final MessageResult handle(MessageEvent<T> event) {
        try {
            // 直接使用已转换的数据
            T data = event.getData();

            // 业务处理
            doHandle(data, event);

            return MessageResult.success(event.getMsgId());

        } catch (Exception e) {
            log.error("消息处理异常: msgId={}, type={}",
                event.getMsgId(), event.getType(), e);
            return MessageResult.error(event.getMsgId(), e.getMessage());
        }
    }

    /**
     * 获取数据类型Class，用于JSON转换
     */
    protected abstract Class<T> getDataClass();

}
