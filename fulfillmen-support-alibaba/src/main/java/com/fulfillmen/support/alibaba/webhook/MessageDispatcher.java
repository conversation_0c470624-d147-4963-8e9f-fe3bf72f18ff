/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息分发器
 * 负责解析原始消息并分发到对应的处理器
 * 
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageDispatcher {

    private final MessageRouter messageRouter;
    private final IdempotentManager idempotentManager;
    private final SignatureValidator signatureValidator;

    /**
     * 处理webhook消息
     * 
     * @param rawMessage 原始消息字符串
     * @param signature  签名
     * @return 处理结果列表
     */
    public List<MessageResult> dispatch(String rawMessage, String signature) {
        log.info("开始处理webhook消息, 消息长度: {}", rawMessage.length());

        try {
            // 1. 验证签名
            if (!signatureValidator.validate(rawMessage, signature)) {
                log.error("消息签名验证失败");
                throw new WebhookProcessingException("消息签名验证失败");
            }

            // 2. 解析消息
            List<RawMessageEvent> rawEvents = parseMessage(rawMessage);
            log.info("解析到 {} 个消息事件", rawEvents.size());

            // 3. 处理每个消息事件
            return rawEvents.stream()
                .map(this::processEvent)
                .toList();

        } catch (Exception e) {
            log.error("处理webhook消息异常", e);
            throw new WebhookProcessingException("处理webhook消息异常", e);
        }
    }

    /**
     * 解析原始消息
     */
    private List<RawMessageEvent> parseMessage(String rawMessage) {
        try {
            return JacksonUtil.convertToBean(rawMessage, new TypeReference<List<RawMessageEvent>>() {
            });
        } catch (Exception e) {
            log.error("解析消息失败: {}", rawMessage, e);
            throw new WebhookProcessingException("解析消息失败", e);
        }
    }

    /**
     * 处理单个消息事件
     */
    private MessageResult processEvent(RawMessageEvent rawEvent) {
        try {
            log.debug("处理消息事件: msgId={}, type={}", rawEvent.getMsgId(), rawEvent.getType());

            // 1. 幂等性检查
            if (idempotentManager.isDuplicate(rawEvent.getMsgId())) {
                log.info("消息已处理过，跳过: msgId={}", rawEvent.getMsgId());
                return MessageResult.duplicate(rawEvent.getMsgId());
            }

            // 2. 转换为类型化事件
            MessageEvent<?> typedEvent = convertToTypedEvent(rawEvent);

            // 3. 路由到处理器
            MessageResult result = messageRouter.route(typedEvent);

            // 4. 记录处理结果
            if (result.isSuccess()) {
                idempotentManager.markProcessed(rawEvent.getMsgId(), result);
            }

            return result;

        } catch (Exception e) {
            log.error("处理消息事件异常: msgId={}", rawEvent.getMsgId(), e);
            return MessageResult.error(rawEvent.getMsgId(), e.getMessage());
        }
    }

    /**
     * 将原始事件转换为类型化事件
     */
    private MessageEvent<?> convertToTypedEvent(RawMessageEvent rawEvent) {
        MessageEvent<Object> event = new MessageEvent<>();
        event.setMsgId(rawEvent.getMsgId());
        event.setGmtBorn(rawEvent.getGmtBorn());
        event.setUserInfo(rawEvent.getUserInfo());
        event.setRawData(JacksonUtil.toJsonString(rawEvent.getData()));

        // 设置消息类型
        CallbackMessageType messageType = MessageTypeFactory.fromValue(rawEvent.getType());
        if (messageType == null) {
            messageType = MessageTypeFactory.getUnknownType(rawEvent.getType());
        }
        event.setType(messageType);

        // 设置数据（保持原始Object类型，由具体处理器进行转换）
        event.setData(rawEvent.getData());

        return event;
    }

    /**
     * 原始消息事件（用于解析JSON）
     */
    @Data
    public static class RawMessageEvent implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 消息ID
         */
        private String msgId;
        /**
         * 消息生成时间戳
         */
        private Long gmtBorn;
        /**
         * 消息数据
         */
        private Object data;
        /**
         * 用户信息
         */
        private String userInfo;
        /**
         * 消息类型
         */
        private String type;

    }
}