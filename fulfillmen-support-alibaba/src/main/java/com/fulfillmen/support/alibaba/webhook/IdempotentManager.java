/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.io.Serial;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;

import org.springframework.stereotype.Component;

import com.fulfillmen.starter.cache.redisson.util.RedisUtils;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 幂等性管理器
 * 负责消息的重复检测和处理状态记录
 * 
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IdempotentManager {

    private static final String PROCESSED_KEY_PREFIX = "webhook:processed:";
    private static final String PROCESSING_KEY_PREFIX = "webhook:processing:";
    // 处理超时时间
    private static final long PROCESSING_TIMEOUT_MINUTES = 5;
    // 已处理记录保存时间
    private static final long PROCESSED_TTL_HOURS = 24;

    /**
     * 检查消息是否重复
     *
     * @param msgId 消息ID
     * @return true表示重复，false表示未处理过
     */
    public boolean isDuplicate(String msgId) {
        try {
            String processedKey = PROCESSED_KEY_PREFIX + msgId;
            String processingKey = PROCESSING_KEY_PREFIX + msgId;

            // 检查是否已经处理完成
            if (RedisUtils.exists(processedKey)) {
                log.debug("消息已处理完成: {}", msgId);
                return true;
            }

            // 检查是否正在处理中（使用分布式锁机制）
            boolean acquired = RedisUtils.tryLock(processingKey, 5, Duration.ofMinutes(PROCESSING_TIMEOUT_MINUTES).toMillis());

            if (!acquired) {
                log.debug("消息正在处理中: {}", msgId);
                return true;
            }

            log.debug("消息未处理过，开始处理: {}", msgId);
            return false;

        } catch (Exception e) {
            log.error("检查消息重复性异常: msgId={}", msgId, e);
            // 异常情况下允许处理，避免阻塞
            return false;
        }
    }

    /**
     * 标记消息为已处理
     *
     * @param msgId  消息ID
     * @param result 处理结果
     */
    public void markProcessed(String msgId, MessageResult result) {
        try {
            String processedKey = PROCESSED_KEY_PREFIX + msgId;
            String processingKey = PROCESSING_KEY_PREFIX + msgId;

            // 记录处理结果
            ProcessedRecord record = new ProcessedRecord();
            record.setMsgId(msgId);
            record.setSuccess(result.isSuccess());
            record.setErrorMessage(result.getErrorMessage());
            record.setProcessedAt(LocalDateTime.now());

            // 处理成功并缓存到Redis, 24小时后过期
            RedisUtils.set(
                processedKey,
                record,
                // RedisUtils使用秒作为单位
                Duration.ofHours(PROCESSED_TTL_HOURS));

            // 删除处理中标记
            RedisUtils.delete(processingKey);

            log.debug("标记消息已处理: msgId={}, success={}", msgId, result.isSuccess());

        } catch (Exception e) {
            log.error("标记消息处理状态异常: msgId={}", msgId, e);
        }
    }

    /**
     * 清理处理中的过期记录（可以通过定时任务调用）
     */
    public void cleanupExpiredProcessing() {
        // 这里可以实现清理逻辑，删除超时的processing记录
        log.debug("清理过期的处理中记录");
    }

    /**
     * 处理记录
     */
    @Data
    public static class ProcessedRecord implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 消息ID
         */
        private String msgId;
        /**
         * 是否处理成功
         */
        private boolean success;
        /**
         * 处理错误信息
         */
        private String errorMessage;
        /**
         * 处理完成时间
         */
        private LocalDateTime processedAt;

    }
}