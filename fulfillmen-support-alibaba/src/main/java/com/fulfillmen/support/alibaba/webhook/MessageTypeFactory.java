/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Optional;

import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.GoodsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;

import lombok.extern.slf4j.Slf4j;

/**
 * 消息类型工厂
 * 负责根据消息类型字符串查找对应的枚举实例
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class MessageTypeFactory {

    /**
     * 根据类型字符串查找对应的消息类型枚举
     * 
     * @param typeString 类型字符串
     * @return 对应的消息类型枚举，如果找不到则返回null
     */
    public static CallbackMessageType fromValue(String typeString) {
        if (typeString == null || typeString.trim().isEmpty()) {
            log.warn("消息类型字符串为空");
            return null;
        }

        // 尝试从订单消息类型中查找
        Optional<CallbackMessageType> orderType = findInEnum(OrderMessageTypeEnums.values(), typeString);
        if (orderType.isPresent()) {
            return orderType.get();
        }

        // 尝试从商品消息类型中查找
        Optional<CallbackMessageType> goodsType = findInEnum(GoodsMessageTypeEnums.values(), typeString);
        if (goodsType.isPresent()) {
            return goodsType.get();
        }

        // 尝试从物流消息类型中查找
        Optional<CallbackMessageType> logisticsType = findInEnum(LogisticsMessageTypeEnums.values(), typeString);
        if (logisticsType.isPresent()) {
            return logisticsType.get();
        }

        log.warn("未知的消息类型: {}", typeString);
        return null;
    }

    /**
     * 在指定的枚举数组中查找匹配的类型
     */
    private static Optional<CallbackMessageType> findInEnum(CallbackMessageType[] enumValues, String typeString) {
        return Arrays.stream(enumValues)
            .filter(enumValue -> typeString.equals(enumValue.getMessageType()))
            .findFirst();
    }

    /**
     * 获取未知消息类型的默认实现
     */
    public static CallbackMessageType getUnknownType(String typeString) {
        return new UnknownMessageType(typeString);
    }

    /**
     * 未知消息类型的默认实现
     */
    public static class UnknownMessageType implements CallbackMessageType, Serializable {

        @Serial
        private static final long serialVersionUID = 1L;
        /**
         * 消息类型
         */
        private final String messageType;

        public UnknownMessageType(String messageType) {
            this.messageType = messageType;
        }

        @Override
        public String getMessageType() {
            return messageType;
        }

        @Override
        public String getMessageDesc() {
            return "未知消息类型: " + messageType;
        }

        @Override
        public String toString() {
            return "UnknownMessageType{" +
                "messageType='" + messageType + '\'' +
                '}';
        }
    }
}