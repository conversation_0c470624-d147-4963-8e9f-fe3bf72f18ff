# 阿里巴巴Webhook消息处理框架

## 概述

这是一个通用的阿里巴巴webhook消息处理框架，提供了完整的消息接收、解析、路由、处理和幂等性保证等功能。框架采用策略模式和模板方法模式，支持灵活的业务扩展。

## 核心特性

### 🚀 核心功能
- **消息签名验证** - HMAC-SHA1签名验证，确保消息安全性
- **幂等性处理** - 基于Redis的消息去重，防止重复处理
- **类型化处理** - 强类型数据转换，避免类型转换错误
- **优先级路由** - 支持处理器优先级，确保正确的处理顺序
- **异常处理** - 完善的异常捕获和错误日志记录
- **配置化管理** - 支持灵活的配置和开关控制

### 📋 支持的消息类型
- 订单消息 (OrderMessageTypeEnums)
- 商品消息 (GoodsMessageTypeEnums)  
- 物流消息 (LogisticsMessageTypeEnums)

## 快速开始

### 1. 配置

在`application.yml`中添加配置：

```yaml
fulfillmen:
  alibaba:
    webhook:
      secret-key: "your-secret-key"              # 签名验证密钥
      enable-signature-validation: true          # 启用签名验证
      enable-idempotency-check: true            # 启用幂等性检查
      processing-timeout-minutes: 5             # 处理超时时间（分钟）
      processed-ttl-hours: 24                   # 已处理消息TTL（小时）
      enable-detailed-logging: true             # 启用详细日志
```

### 2. 创建消息处理器

继承`AbstractTypedMessageHandler`创建具体的消息处理器：

```java
@Component
@Slf4j
public class OrderMessageHandler extends AbstractTypedMessageHandler<OrderData> {
    
    public OrderMessageHandler() {
        // 指定支持的消息类型
        super(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
    }
    
    @Override
    protected Class<OrderData> getDataClass() {
        return OrderData.class;
    }
    
    @Override
    protected void doHandle(OrderData data, MessageEvent<OrderData> event) {
        // 实现具体的业务逻辑
        log.info("处理订单消息: orderId={}", data.getOrderId());
        
        // 业务处理逻辑...
    }
    
    @Override
    public int getPriority() {
        return 100; // 设置处理器优先级
    }
}
```

### 3. 定义数据模型

```java
public class OrderData {
    private Long orderId;
    private String currentStatus;
    private String buyerMemberId;
    private String sellerMemberId;
    
    // getters and setters...
}
```

### 4. 接收Webhook

框架提供了默认的Webhook接收端点：

```
POST /api/alibaba/webhook/callback
```

请求头：
- `signature`: 阿里巴巴的签名

请求体：JSON格式的消息数组

## 架构设计

### 核心组件

1. **MessageDispatcher** - 消息分发器
   - 验证签名
   - 解析消息
   - 幂等性检查
   - 路由到对应处理器

2. **MessageRouter** - 消息路由器
   - 根据消息类型查找处理器
   - 支持优先级排序
   - 数据类型转换

3. **IdempotentManager** - 幂等性管理器
   - 基于Redis的消息去重
   - 分布式锁防止并发
   - TTL自动清理

4. **SignatureValidator** - 签名验证器
   - HMAC-SHA1算法验证
   - 可配置开关

### 处理流程

```
Webhook请求 → 签名验证 → 消息解析 → 幂等性检查 → 消息路由 → 业务处理 → 结果返回
```

## 高级功能

### 自定义消息类型

1. 实现`CallbackMessageType`接口：

```java
public enum CustomMessageType implements CallbackMessageType {
    CUSTOM_MESSAGE("CUSTOM_MESSAGE", "自定义消息");
    
    private final String messageType;
    private final String messageDesc;
    
    // 构造器和getter方法...
}
```

2. 在`MessageTypeFactory`中添加支持：

```java
// 在fromValue方法中添加自定义类型的查找逻辑
```

### 异步处理

框架支持异步处理配置：

```yaml
fulfillmen:
  alibaba:
    webhook:
      enable-async-processing: true
      async-thread-pool-size: 10
```

### 监控和日志

框架提供详细的处理日志：

```java
log.info("收到webhook消息: msgId={}, type={}", msgId, messageType);
log.info("消息处理完成: msgId={}, 耗时={}ms", msgId, duration);
```

## 示例代码

参考`OrderStatusChangeHandler`类，展示了完整的消息处理器实现示例。

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| secret-key | - | 签名验证密钥 |
| enable-signature-validation | true | 是否启用签名验证 |
| enable-idempotency-check | true | 是否启用幂等性检查 |
| processing-timeout-minutes | 5 | 处理超时时间 |
| processed-ttl-hours | 24 | 已处理消息TTL |
| enable-async-processing | false | 是否启用异步处理 |
| async-thread-pool-size | 10 | 异步线程池大小 |
| enable-detailed-logging | true | 是否启用详细日志 |

## 注意事项

1. **幂等性设计** - 消息处理必须是幂等的，重复处理不会产生副作用
2. **异常处理** - 处理器中的异常会被框架捕获并记录
3. **性能考虑** - 建议使用异步处理对于耗时的业务逻辑
4. **监控告警** - 建议对处理失败的消息进行监控和告警

## 扩展指南

1. **添加新的消息类型** - 实现CallbackMessageType接口
2. **自定义路由逻辑** - 继承MessageRouter类
3. **自定义幂等性策略** - 实现IdempotentManager接口
4. **自定义异常处理** - 扩展WebhookProcessingException

这个框架为阿里巴巴webhook消息处理提供了完整、灵活、可扩展的解决方案。