/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.fulfillmen.support.alibaba.enums.CallbackMessageType;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息路由器
 * 负责将消息事件路由到合适的处理器
 * 
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@Component
public class MessageRouter {

    private final List<MessageHandler<?>> handlers;
    private final Map<CallbackMessageType, List<MessageHandler<?>>> handlerMap;

    public MessageRouter(List<MessageHandler<?>> handlers) {
        this.handlers = handlers;
        this.handlerMap = new HashMap<>();
    }

    /**
     * 路由消息到合适的处理器
     */
    @SuppressWarnings("unchecked")
    public MessageResult route(MessageEvent<?> event) {
        List<MessageHandler<?>> applicableHandlers = handlerMap.get(event.getType());

        if (CollectionUtils.isEmpty(applicableHandlers)) {
            log.warn("未找到消息类型的处理器: {}", event.getType());
            return MessageResult.noHandler(event.getMsgId());
        }

        // 按优先级排序，选择第一个处理器
        MessageHandler<?> handler = applicableHandlers.stream()
            .min(Comparator.comparing(MessageHandler::getPriority))
            .orElse(applicableHandlers.get(0));

        // 类型转换处理
        return ((MessageHandler<Object>) handler).handle((MessageEvent<Object>) event);
    }

    /**
     * 注册处理器
     */
    @PostConstruct
    public void registerHandlers() {
        // 自动发现并注册所有处理器
        handlerMap.clear();
        for (MessageHandler<?> handler : handlers) {
            for (CallbackMessageType type : handler.getSupportedTypes()) {
                handlerMap.computeIfAbsent(type, k -> new ArrayList<>())
                    .add(handler);
            }
        }
    }
}
