/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 商品消息
 *
 * <AUTHOR>
 * @date 2025/7/9 19:09
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum GoodsMessageTypeEnums implements CallbackMessageType {

    /**
     * 1688产品下架（关系用户视角）
     */
    PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE("PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE", "1688产品下架（关系用户视角）"),
    /**
     * 1688产品新增或修改（关系用户视角）
     */
    PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY("PRODUCT_RELATION_VIEW_PRODUCT_NEW_OR_MODIFY", "1688产品新增或修改（关系用户视角）"),
    /**
     * 1688产品删除（关系用户视角）
     */
    PRODUCT_RELATION_VIEW_PRODUCT_DELETE("PRODUCT_RELATION_VIEW_PRODUCT_DELETE", "1688产品删除（关系用户视角）"),
    /**
     * 1688产品上架（关系用户视角）
     */
    PRODUCT_RELATION_VIEW_PRODUCT_REPOST("PRODUCT_RELATION_VIEW_PRODUCT_REPOST", "1688产品上架（关系用户视角）"),
    /**
     * 1688商品库存变更消息（关系用户视角）
     */
    PRODUCT_PRODUCT_INVENTORY_CHANGE("PRODUCT_PRODUCT_INVENTORY_CHANGE", "1688商品库存变更消息（关系用户视角）"),
    /**
     * 精选货源商品下架消息
     */
    PRODUCT_PFT_OFFER_QUIT("PRODUCT_PFT_OFFER_QUIT", "精选货源商品下架消息"),
    /**
     * 精选货源商品价格变动消息
     */
    PRODUCT_PFT_OFFER_PRICE_MODIFY("PRODUCT_PFT_OFFER_PRICE_MODIFY", "精选货源商品价格变动消息"),
    /**
     * 一键铺货消息
     */
    PRODUCT_PRODUCT_CROSSBOARD_INFORM("PRODUCT_PRODUCT_CROSSBOARD_INFORM", "一键铺货消息"),
    /**
     * 跨境设为货源
     */
    CROSSBOARD_CROSSBOARD_ADD_SUPPLY("CROSSBOARD_CROSSBOARD_ADD_SUPPLY", "跨境设为货源"),
    /**
     * 1688产品审核（关系用户视角）
     */
    PRODUCT_RELATION_VIEW_PRODUCT_AUDIT("PRODUCT_RELATION_VIEW_PRODUCT_AUDIT", "跨境货源审核"),
    ;

    private final String messageType;
    private final String messageDesc;

    GoodsMessageTypeEnums(String messageType, String messageDesc) {
        this.messageType = messageType;
        this.messageDesc = messageDesc;
    }

}
