/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.util.List;

import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException;

/**
 * 消息处理器接口
 * 
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 * @param <T> 消息数据类型
 */
public interface MessageHandler<T> {

    /**
     * 判断是否能处理此类型消息
     * 
     * @param messageType
     * @return 如果能处理返回true，否则返回false
     */
    boolean canHandle(CallbackMessageType messageType);

    /**
     * 处理消息的核心方法
     * 
     * @param event
     * @return 处理结果
     * @throws WebhookProcessingException 如果处理过程中发生错误
     */
    MessageResult handle(MessageEvent<T> event);

    /**
     * 获取处理器优先级（数字越小优先级越高）
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 0;
    }

    /**
     * 获取支持的消息类型列表
     * 
     * @return 支持的消息类型列表
     */
    List<CallbackMessageType> getSupportedTypes();

}
