/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.enums.CallbackMessageType;

import lombok.Data;

/**
 * 消息事件类
 * 用于封装从阿里巴巴回调接口接收到的消息数据
 * 包含消息ID、时间戳、数据内容、原始数据字符串等
 * 支持泛型数据类型，方便处理不同类型的消息内容
 * 
 * <pre>
 * 示例:
 * {
 * "msgId": "12345",
 * "gmtBorn": "1392711616045",
 * "data": {
 * "key1": "value1"
 * },
 * "userInfo": "memeberId",
 * "type": "messageType"
 * }
 * 
 * <AUTHOR>
 * @date 2025/7/10 09:49
 * @description: todo
 * @since 1.0.0
 */
@Data
public class MessageEvent<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息 id 唯一标识
     */
    private String msgId;

    /**
     * 消息推送时间
     */
    private Long gmtBorn;

    /**
     * 消息内容（泛型支持）
     */
    private T data;

    /**
     * 原始数据字符串（保留用于调试）
     */
    private String rawData;

    /**
     * 消息关联的用户 memberId
     */
    private String userInfo;

    /**
     * 消息类型
     */
    private CallbackMessageType type;

    /**
     * 消息接收时间
     */
    private LocalDateTime receivedAt;

    /**
     * 获取类型安全的数据
     */
    public <U> U getTypedData(Class<U> clazz) {
        return JacksonUtil.convertToBean(rawData, clazz);
    }

    // 1688 创建订单 买家视角
    // {
    // "orderId": 167539019420540000,
    // "currentStatus": "waitbuyerpay",
    // "msgSendTime": "2018-05-30 19: 24: 18",
    // "buyerMemberId": "b2b-665170100",
    // "sellerMemberId": "b2b-1676547900b7bb3"
    // }
    //
    // {
    // "orderId": 167539019420540000,
    // "currentStatus": "waitbuyerpay",
    // "msgSendTime": "2018-05-30 19: 24: 18",
    // "buyerMemberId": "b2b-665170100",
    // "sellerMemberId": "b2b-1676547900b7bb3"
    // }

    // 1688 订单发货
    // {
    // "orderId": 167539019420540000,
    // "currentStatus": "waitbuyerreceive",
    // "msgSendTime": "2018-05-30 19: 34: 27",
    // "buyerMemberId": "b2b-665170100",
    // "sellerMemberId": "b2b-1676547900b7bb3"
    // }

    // 1688运营后台关闭订单（买家视角） ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE
    // 1688买家关闭订单（买家视角）/buyer closing order (buyer view)
    // ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE
    // 备注：1688运营后台关闭订单，付款超时关闭，包括买家拍下未付款，系统自动关闭订单情况
    // {
    // "orderId": 167539019420540000,
    // "currentStatus": "cancel",
    // "msgSendTime": "2018-05-30 19: 34: 27",
    // "buyerMemberId": "b2b-665170100",
    // "sellerMemberId": "b2b-1676547900b7bb3"
    // }

}
