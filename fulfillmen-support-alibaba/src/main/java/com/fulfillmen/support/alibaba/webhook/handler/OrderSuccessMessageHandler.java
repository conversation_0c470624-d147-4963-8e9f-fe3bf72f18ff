/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.handler;

import org.springframework.stereotype.Component;

import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderSuccessData;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单成功消息处理器
 * 处理 ORDER_BUYER_VIEW_ORDER_SUCCESS 类型的消息
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@Component
public class OrderSuccessMessageHandler extends AbstractTypedMessageHandler<OrderSuccessData> {

    public OrderSuccessMessageHandler() {
        super(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);
    }

    @Override
    protected Class<OrderSuccessData> getDataClass() {
        return OrderSuccessData.class;
    }

    @Override
    protected void doHandle(OrderSuccessData data, MessageEvent<OrderSuccessData> event) {
        log.info("处理订单成功消息: orderId={}, buyerMemberId={}, sellerMemberId={}, status={}", 
            data.getOrderId(), data.getBuyerMemberId(), data.getSellerMemberId(), data.getCurrentStatus());
        
        // 这里可以添加具体的业务处理逻辑
        // 例如：更新订单状态、发送通知、记录日志等
        
        log.info("订单成功消息处理完成: orderId={}", data.getOrderId());
    }

    @Override
    public int getPriority() {
        return 100;
    }
}
