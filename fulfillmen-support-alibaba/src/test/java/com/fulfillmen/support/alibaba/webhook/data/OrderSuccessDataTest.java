/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.data;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import com.fulfillmen.starter.core.util.JacksonUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * OrderSuccessData 测试类
 * 测试订单成功消息数据模型的序列化、反序列化和数据操作
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class OrderSuccessDataTest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    private OrderSuccessData orderSuccessData;

    /**
     * 测试用例的真实数据 - 基于用户提供的消息
     */
    private static final String SAMPLE_JSON = """
        {
            "buyerMemberId": "b2b-2207416548807a4d12",
            "currentStatus": "success",
            "orderId": 2806599398122540788,
            "sellerMemberId": "b2b-221280776451649a09",
            "msgSendTime": "2025-07-10 17:55:46"
        }
        """;

    @BeforeEach
    void setUp() {
        orderSuccessData = new OrderSuccessData();
        orderSuccessData.setBuyerMemberId("b2b-2207416548807a4d12");
        orderSuccessData.setCurrentStatus("success");
        orderSuccessData.setOrderId(2806599398122540788L);
        orderSuccessData.setSellerMemberId("b2b-221280776451649a09");
        orderSuccessData.setMsgSendTime("2025-07-10 17:55:46");
    }

    @Test
    @Tag("data")
    void shouldCreateOrderSuccessDataWithAllFields() {
        log.info("{} 开始测试OrderSuccessData字段设置 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given & When - 在setUp中已设置

        // Then
        assertThat(orderSuccessData.getBuyerMemberId()).isEqualTo("b2b-2207416548807a4d12");
        assertThat(orderSuccessData.getCurrentStatus()).isEqualTo("success");
        assertThat(orderSuccessData.getOrderId()).isEqualTo(2806599398122540788L);
        assertThat(orderSuccessData.getSellerMemberId()).isEqualTo("b2b-221280776451649a09");
        assertThat(orderSuccessData.getMsgSendTime()).isEqualTo("2025-07-10 17:55:46");

        log.info("{}买家会员ID: {}", LOG_ITEM, orderSuccessData.getBuyerMemberId());
        log.info("{}订单状态: {}", LOG_ITEM, orderSuccessData.getCurrentStatus());
        log.info("{}订单ID: {}", LOG_ITEM, orderSuccessData.getOrderId());
        log.info("{}卖家会员ID: {}", LOG_ITEM, orderSuccessData.getSellerMemberId());
        log.info("{}消息发送时间: {}", LOG_ITEM, orderSuccessData.getMsgSendTime());

        log.info("{} 测试OrderSuccessData字段设置完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("serialization")
    void shouldSerializeToJson() {
        log.info("{} 开始测试OrderSuccessData序列化 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // When
        String json = JacksonUtil.toJsonString(orderSuccessData);

        // Then
        assertThat(json).isNotNull();
        assertThat(json).isNotEmpty();
        assertThat(json).contains("\"buyerMemberId\":\"b2b-2207416548807a4d12\"");
        assertThat(json).contains("\"currentStatus\":\"success\"");
        assertThat(json).contains("\"orderId\":2806599398122540788");
        assertThat(json).contains("\"sellerMemberId\":\"b2b-221280776451649a09\"");
        assertThat(json).contains("\"msgSendTime\":\"2025-07-10 17:55:46\"");

        log.info("{}序列化结果: {}", LOG_ITEM, json);

        log.info("{} 测试OrderSuccessData序列化完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("deserialization")
    void shouldDeserializeFromJson() {
        log.info("{} 开始测试OrderSuccessData反序列化 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        log.info("{}输入JSON: {}", LOG_ITEM, SAMPLE_JSON.trim());

        // When
        OrderSuccessData deserializedData = JacksonUtil.convertToBean(SAMPLE_JSON, OrderSuccessData.class);

        // Then
        assertThat(deserializedData).isNotNull();
        assertThat(deserializedData.getBuyerMemberId()).isEqualTo("b2b-2207416548807a4d12");
        assertThat(deserializedData.getCurrentStatus()).isEqualTo("success");
        assertThat(deserializedData.getOrderId()).isEqualTo(2806599398122540788L);
        assertThat(deserializedData.getSellerMemberId()).isEqualTo("b2b-221280776451649a09");
        assertThat(deserializedData.getMsgSendTime()).isEqualTo("2025-07-10 17:55:46");

        log.info("{}反序列化结果 - 买家会员ID: {}", LOG_ITEM, deserializedData.getBuyerMemberId());
        log.info("{}反序列化结果 - 订单状态: {}", LOG_ITEM, deserializedData.getCurrentStatus());
        log.info("{}反序列化结果 - 订单ID: {}", LOG_ITEM, deserializedData.getOrderId());
        log.info("{}反序列化结果 - 卖家会员ID: {}", LOG_ITEM, deserializedData.getSellerMemberId());
        log.info("{}反序列化结果 - 消息发送时间: {}", LOG_ITEM, deserializedData.getMsgSendTime());

        log.info("{} 测试OrderSuccessData反序列化完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("serialization")
    void shouldSerializeAndDeserializeCorrectly() {
        log.info("{} 开始测试OrderSuccessData序列化反序列化一致性 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // When
        String json = JacksonUtil.toJsonString(orderSuccessData);
        OrderSuccessData deserializedData = JacksonUtil.convertToBean(json, OrderSuccessData.class);

        // Then
        assertThat(deserializedData).isNotNull();
        assertThat(deserializedData.getBuyerMemberId()).isEqualTo(orderSuccessData.getBuyerMemberId());
        assertThat(deserializedData.getCurrentStatus()).isEqualTo(orderSuccessData.getCurrentStatus());
        assertThat(deserializedData.getOrderId()).isEqualTo(orderSuccessData.getOrderId());
        assertThat(deserializedData.getSellerMemberId()).isEqualTo(orderSuccessData.getSellerMemberId());
        assertThat(deserializedData.getMsgSendTime()).isEqualTo(orderSuccessData.getMsgSendTime());

        log.info("{}序列化反序列化一致性验证通过", LOG_ITEM);

        log.info("{} 测试OrderSuccessData序列化反序列化一致性完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("data")
    void shouldHandleNullValues() {
        log.info("{} 开始测试OrderSuccessData空值处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        OrderSuccessData emptyData = new OrderSuccessData();

        // When & Then
        assertThat(emptyData.getBuyerMemberId()).isNull();
        assertThat(emptyData.getCurrentStatus()).isNull();
        assertThat(emptyData.getOrderId()).isNull();
        assertThat(emptyData.getSellerMemberId()).isNull();
        assertThat(emptyData.getMsgSendTime()).isNull();

        log.info("{}空值处理验证通过", LOG_ITEM);

        log.info("{} 测试OrderSuccessData空值处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("data")
    void shouldHandlePartialData() {
        log.info("{} 开始测试OrderSuccessData部分数据处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String partialJson = """
            {
                "orderId": 2806599398122540788,
                "currentStatus": "success"
            }
            """;

        log.info("{}部分数据JSON: {}", LOG_ITEM, partialJson.trim());

        // When
        OrderSuccessData partialData = JacksonUtil.convertToBean(partialJson, OrderSuccessData.class);

        // Then
        assertThat(partialData).isNotNull();
        assertThat(partialData.getOrderId()).isEqualTo(2806599398122540788L);
        assertThat(partialData.getCurrentStatus()).isEqualTo("success");
        assertThat(partialData.getBuyerMemberId()).isNull();
        assertThat(partialData.getSellerMemberId()).isNull();
        assertThat(partialData.getMsgSendTime()).isNull();

        log.info("{}部分数据处理验证通过", LOG_ITEM);

        log.info("{} 测试OrderSuccessData部分数据处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("validation")
    void shouldValidateOrderIdFormat() {
        log.info("{} 开始测试OrderSuccessData订单ID格式验证 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        Long validOrderId = 2806599398122540788L;
        Long invalidOrderId = -1L;

        // When & Then
        orderSuccessData.setOrderId(validOrderId);
        assertThat(orderSuccessData.getOrderId()).isEqualTo(validOrderId);
        assertThat(orderSuccessData.getOrderId()).isPositive();

        orderSuccessData.setOrderId(invalidOrderId);
        assertThat(orderSuccessData.getOrderId()).isEqualTo(invalidOrderId);

        log.info("{}有效订单ID: {}", LOG_ITEM, validOrderId);
        log.info("{}无效订单ID: {}", LOG_ITEM, invalidOrderId);

        log.info("{} 测试OrderSuccessData订单ID格式验证完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
