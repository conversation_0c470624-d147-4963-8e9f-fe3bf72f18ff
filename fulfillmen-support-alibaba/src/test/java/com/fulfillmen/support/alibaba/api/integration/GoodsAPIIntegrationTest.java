/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.integration;

import static org.assertj.core.api.Assertions.assertThat;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsCouponClaimRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsImageUploadRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsKeywordNavigationRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsRelatedRecommendRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSearchRequestRecord.SaleFilterParam;
import com.fulfillmen.support.alibaba.api.request.goods.GoodsSellerRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import com.fulfillmen.support.alibaba.service.IGoodsService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.FileCopyUtils;
import reactor.test.StepVerifier;

/**
 * 1688商品API集成测试 这个测试类会真实调用1688 API，请确保有正确的配置和测试数据
 *
 * <AUTHOR>
 * @created 2025-01-10
 */
@Slf4j
@Tag("integration")
@Tag("goods")
class GoodsAPIIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private IGoodsService goodsService;

    @Test
    @Tag("read")
    void shouldSearchGoods() {
        // Given
//        var request = GoodsSearchRequestRecord.of("裙子", LanguageEnum.EN);
        List<SaleFilterParam> saleFilterParams = new ArrayList<>();
        saleFilterParams.add(SaleFilterParam.builder().saleType("sales7").saleStart("100").saleEnd("1000").build());
        var request = GoodsSearchRequestRecord.builder()
            .keyword("裙子")
            .country(LanguageEnum.EN.getLanguage())
            .pageSize(20)
            .beginPage(1)
            .saleFilterList(saleFilterParams)
            .build();
        log.info("{}开始搜索商品集成测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.searchGoods(request)).assertNext(searchResponse -> {
            // 基本响应检查
            assertThat(searchResponse).isNotNull();
            assertThat(searchResponse.getResult()).isNotNull();

            if (!searchResponse.getResult().getSuccess()) {
                log.error("{}搜索失败: {}", LOG_ITEM, searchResponse.getResult().getCode());
                log.error("{}错误信息: {}", LOG_ITEM, searchResponse.getResult().getMessage());
                return;
            }

            // 数据检查
            var searchResult = searchResponse.getResult().getResult();
            assertThat(searchResult.getData()).isNotEmpty();

            // 记录结果
            log.info("{}搜索结果: 总数={}, 当前页商品数={}", LOG_ITEM, searchResult.getTotalRecords(), searchResult.getData()
                .size());

            // 记录性能指标
            recordMetrics("SearchGoods", startTime, true);
        }).verifyComplete();

        // 打印性能指标
        log.info("{} 性能指标 {}", LOG_SEPARATOR, LOG_SEPARATOR);
        logMetrics("SearchGoods");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldUploadImage() throws IOException {
        // Given
        Resource imageResource = new ClassPathResource("images/solar-dog.png");
        byte[] imageBytes = FileCopyUtils.copyToByteArray(imageResource.getInputStream());
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);

        var request = GoodsImageUploadRequestRecord.of(base64Image, null);

        log.info("{}开始图片上传测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: imageBase64长度={}", LOG_ITEM, request.imageBase64().length());

        // 重置指标计数器
        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.uploadImage(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isEqualTo("true");
            assertThat(response.getResult().getResult()).isNotEmpty();

            log.info("{}上传结果: imageId={}", LOG_ITEM, response.getResult().getResult());

            recordMetrics("UploadImage", startTime, true);
        }).verifyComplete();

        logMetrics("UploadImage");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldSearchGoodsByImage() {
        // Given
        var request = GoodsImageSearchRequestRecord.of("1024108358521950220", LanguageEnum.EN);

        log.info("{}开始图片搜索测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.searchGoodsByImage(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isEqualTo("true");
            assertThat(response.getResult().getResult().getData()).isNotEmpty();

            log.info("{}搜索结果: 总数={}, 当前页商品数={}", LOG_ITEM, response.getResult().getResult().getTotalRecords(), response
                .getResult()
                .getResult()
                .getData()
                .size());

            recordMetrics("ImageSearch", startTime, true);
        }).verifyComplete();

        logMetrics("ImageSearch");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetGoodsDetail() {
        // Given
        var request = GoodsDetailRequestRecord.of(882372003002L, LanguageEnum.EN);

        log.info("{}开始获取商品详情测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.getGoodsDetail(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();

            var detail = response.getResult().getResult();
            assertThat(detail).isNotNull();
            assertThat(detail.getOfferId()).isNotNull();

            // 验证基本字段
            assertThat(detail.getOfferId()).isEqualTo(882372003002L);
            assertThat(detail.getCategoryId()).isEqualTo(122984003L);
            assertThat(detail.getSubject()).isEqualTo("2代6件A款凹饼电影同款魔彤模型蛋糕摆件公仔厂家直销");
            assertThat(detail.getSubjectTrans()).contains("magic child model");

            // 验证图片信息
            assertThat(detail.getProductImage()).isNotNull();
            assertThat(detail.getProductImage().getImages()).hasSize(5);
            assertThat(detail.getProductImage().getImages().get(0))
                .isEqualTo("https://cbu01.alicdn.com/img/ibank/O1CN01YApDSy1G8lxOF1BxJ_!!*************-0-cib.jpg");

            // 验证商品属性
            assertThat(detail.getProductAttribute()).hasSizeGreaterThanOrEqualTo(1);
            var attr = detail.getProductAttribute().get(0);
            assertThat(attr.getAttributeId()).isEqualTo("287");
            assertThat(attr.getAttributeName()).isEqualTo("材质");
            assertThat(attr.getValue()).isEqualTo("PVC");

            // 验证 SKU 信息
            assertThat(detail.getProductSkuInfos()).hasSizeGreaterThanOrEqualTo(1);
            var sku = detail.getProductSkuInfos().get(0);
            assertThat(sku.getAmountOnSale()).isNotNull();
            assertThat(sku.getPrice()).isEqualTo("37.0");
            assertThat(sku.getSkuId()).isEqualTo(5724991804055L);
            assertThat(sku.getCargoNumber()).isEqualTo("PVC-**********");
            assertThat(sku.getFenxiaoPriceInfo().getOfferPrice()).isEqualTo("37");

            // 验证销售信息
            assertThat(detail.getProductSaleInfo()).isNotNull();
            assertThat(detail.getProductSaleInfo().getAmountOnSale()).isNotNull();
            assertThat(detail.getProductSaleInfo().getQuoteType()).isEqualTo(1);
            assertThat(detail.getProductSaleInfo().getUnitInfo().getUnit()).isEqualTo("套");
            assertThat(detail.getProductSaleInfo().getFenxiaoSaleInfo().getStartQuantity()).isEqualTo(3);

            // 验证物流信息
            assertThat(detail.getProductShippingInfo()).isNotNull();
            assertThat(detail.getProductShippingInfo().getSendGoodsAddressText()).isEqualTo("广东省汕头市");
            assertThat(detail.getProductShippingInfo().getSkuShippingInfoList().get(0).getWeight()).isEqualTo(550);

            // 验证其他基本信息
            assertThat(detail.getIsJxhy()).isFalse();
            assertThat(detail.getSellerOpenId()).isEqualTo("BBBmjKf4K_FKsazpQuoIuOgIQ");
            assertThat(detail.getMinOrderQuantity()).isEqualTo(1);
            assertThat(detail.getStatus()).isEqualTo("published");
            assertThat(detail.getCreateDate()).isEqualTo("2025-02-01 14:31:21");
            assertThat(detail.getSoldOut()).isNotEmpty();
            assertThat(detail.getTradeScore()).isEqualTo("5.0");
            assertThat(detail.getOfferIdentities()).containsExactly("tp_member");
            assertThat(detail.getPromotionUrl())
                .isEqualTo("https://detail.1688.com/offer/882372003002.html?tdScene=kj-ai");

            recordMetrics("GoodsDetail", startTime, true);
        }).verifyComplete();

        logMetrics("GoodsDetail");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetSellerGoods() {
        // Given
        var request = GoodsSellerRequestRecord.of(LanguageEnum.EN, "1");

        log.info("{}开始获取卖家商品测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.getSellerGoods(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();

            var result = response.getResult().getResult();

            log.info("{}卖家商品: 总数={}, 当前页商品数={}", LOG_ITEM, result.getTotalRecords(), result.getData().length);

            recordMetrics("SellerGoods", startTime, true);
        }).verifyComplete();

        logMetrics("SellerGoods");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldRecommendGoods() {
        // Given
        var request = GoodsRecommendRequestRecord.of(LanguageEnum.EN);

        log.info("{}开始商品推荐测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.recommendGoods(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();

            var result = response.getResult().getResult();
            assertThat(result).isNotNull();
            assertThat(result).isNotEmpty();

            log.info("{}推荐商品数: {}", LOG_ITEM, result.length);

            recordMetrics("RecommendGoods", startTime, true);
        }).verifyComplete();

        logMetrics("RecommendGoods");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetKeywordNavigation() {
        // Given
        var request = GoodsKeywordNavigationRequestRecord.of("裙子", LanguageEnum.EN, "US", "USD");

        log.info("{}开始关键词导航测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.getKeywordNavigation(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();

            var result = response.getResult().getResult();
            assertThat(result).isNotNull();

            if (result.size() > 0) {
                var firstCategory = result.get(0);
                log.info("{}导航结果: 分类名称={}, 翻译={}", LOG_ITEM, firstCategory.getName(), firstCategory.getTranslateName());
            }

            recordMetrics("KeywordNavigation", startTime, true);
        }).verifyComplete();

        logMetrics("KeywordNavigation");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldGetRelatedRecommend() {
        // Given
        var request = GoodsRelatedRecommendRequestRecord.of(851558761905L, LanguageEnum.EN, 1, 5);

        log.info("{}开始相关商品推荐测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.getRelatedRecommend(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();

            var result = response.getResult().getResult();
            assertThat(result).isNotNull();
            assertThat(result).isNotEmpty();

            log.info("{}相关商品数: {}", LOG_ITEM, result.length);

            recordMetrics("RelatedRecommend", startTime, true);
        }).verifyComplete();

        logMetrics("RelatedRecommend");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("write")
    void shouldClaimCoupon() {
        // Given
        var request = GoodsCouponClaimRequestRecord.of(Arrays.asList(625035063193L, 813672601144L));

        log.info("{}开始优惠券领取测试{}", LOG_SEPARATOR, LOG_SEPARATOR);
        log.info("{}请求参数: {}", LOG_ITEM, request);

        resetMetrics();
        long startTime = System.currentTimeMillis();

        // When & Then
        StepVerifier.create(goodsService.claimCoupon(request)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isTrue();

            var result = response.getResult().getResult();
            assertThat(result).isNotNull();

            log.info("{}领取结果: 优惠券ID={}", LOG_ITEM, result.getCouponIds());

            recordMetrics("ClaimCoupon", startTime, true);
        }).verifyComplete();

        logMetrics("ClaimCoupon");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("read")
    void shouldSearchGoodsByUploadedImage() throws IOException {
        log.info("{}开始图片上传并搜索测试{}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Step 1: 上传图片
        Resource imageResource = new ClassPathResource("images/solar-dog.png");
        byte[] imageBytes = FileCopyUtils.copyToByteArray(imageResource.getInputStream());
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);

        var uploadRequest = GoodsImageUploadRequestRecord.of(base64Image, null);

        log.info("{}Step 1 - 上传图片: imageBase64长度={}", LOG_ITEM, uploadRequest.imageBase64().length());

        // 重置指标计数器
        resetMetrics();
        long uploadStartTime = System.currentTimeMillis();

        // 执行上传并获取imageId
        String imageId = goodsService.uploadImage(uploadRequest).map(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isEqualTo("true");
            assertThat(response.getResult().getResult()).isNotEmpty();

            log.info("{}上传结果: imageId={}", LOG_ITEM, response.getResult().getResult());
            recordMetrics("UploadImage", uploadStartTime, true);

            return response.getResult().getResult();
        }).block();

        logMetrics("UploadImage");

        // Step 2: 使用imageId搜索商品
        var searchRequest = GoodsImageSearchRequestRecord.of(imageId, LanguageEnum.EN);

        log.info("{}Step 2 - 图片搜索: 使用imageId={}", LOG_ITEM, imageId);

        long searchStartTime = System.currentTimeMillis();

        StepVerifier.create(goodsService.searchGoodsByImage(searchRequest)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isEqualTo("true");
            assertThat(response.getResult().getResult().getData()).isNotEmpty();

            log.info("{}搜索结果: 总数={}, 当前页商品数={}", LOG_ITEM, response.getResult().getResult().getTotalRecords(), response
                .getResult()
                .getResult()
                .getData()
                .size());

            recordMetrics("ImageSearch", searchStartTime, true);
        }).verifyComplete();

        logMetrics("ImageSearch");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("测试图片搜索 URL")
    void shouldSearchGoodsByUploadedImageUrl() throws IOException {
        log.info("{}开始图片上传并搜索测试{}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Step 1: 上传图片
        Resource imageResource = new ClassPathResource("images/solar-dog.png");
        byte[] imageBytes = FileCopyUtils.copyToByteArray(imageResource.getInputStream());
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);

        var uploadRequest = GoodsImageUploadRequestRecord.of(base64Image, null);

        log.info("{}Step 1 - 上传图片: imageBase64长度={}", LOG_ITEM, uploadRequest.imageBase64().length());

        // 重置指标计数器
        resetMetrics();
        logMetrics("UploadImage");

        // Step 2: 使用imageUrl搜索商品
        var searchRequest = GoodsImageSearchRequestRecord.builder()
            .imageAddress("https://cbu01.alicdn.com/img/ibank/O1CN012XSR7E1j8QWTzoBvG_!!*************-0-cib.jpg")
            .country(LanguageEnum.EN.getLanguage())
            .pageSize(20)
            .beginPage(1)
            .build();

        log.info("{}Step 2 - 图片搜索: 使用imageId={}", LOG_ITEM, searchRequest.imageAddress());

        long searchStartTime = System.currentTimeMillis();

        StepVerifier.create(goodsService.searchGoodsByImage(searchRequest)).assertNext(response -> {
            assertThat(response).isNotNull();
            assertThat(response.getResult()).isNotNull();
            assertThat(response.getResult().getSuccess()).isEqualTo("true");
            assertThat(response.getResult().getResult().getData()).isNotEmpty();

            log.info("{}搜索结果: 总数={}, 当前页商品数={}", LOG_ITEM, response.getResult().getResult().getTotalRecords(), response
                .getResult()
                .getResult()
                .getData()
                .size());

            recordMetrics("ImageSearch", searchStartTime, true);
        }).verifyComplete();

        logMetrics("ImageSearch");
        log.info("{} 测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}