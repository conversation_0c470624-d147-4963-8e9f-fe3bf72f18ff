/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.webhook.controller.WebhookController;
import com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException;

import lombok.extern.slf4j.Slf4j;

/**
 * WebhookController Mock测试
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class WebhookControllerMockTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @Autowired
    private WebhookController webhookController;

    @MockBean
    private MessageDispatcher messageDispatcher;

    /**
     * 测试用例的消息数据 - 基于用户提供的真实数据
     */
    private static final String VALID_MESSAGE_BODY = """
        [{
            "bizKey": "2806599398122540788",
            "data": {
                "buyerMemberId": "b2b-2207416548807a4d12",
                "currentStatus": "success",
                "orderId": 2806599398122540788,
                "sellerMemberId": "b2b-221280776451649a09",
                "msgSendTime": "2025-07-10 17:55:46"
            },
            "gmtBorn": 1752141346107,
            "msgId": "139830976934",
            "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
            "userInfo": "b2b-2207416548807a4d12"
        }]
        """;

    private static final String VALID_SIGNATURE = "38DF23A2911926BD925AA1D89DC2D282777BA572";

    @Test
    @Tag("success")
    void shouldHandleValidWebhookMessage() {
        log.info("{} 开始测试正常的webhook消息处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        log.info("{}请求消息长度: {}", LOG_ITEM, VALID_MESSAGE_BODY.length());
        log.info("{}签名: {}", LOG_ITEM, VALID_SIGNATURE);

        // Mock成功的处理结果
        MessageResult successResult = MessageResult.success("139830976934", "订单处理成功");
        when(messageDispatcher.dispatch(anyString(), anyString()))
            .thenReturn(Arrays.asList(successResult));

        // When
        var response = webhookController.handleCallback(VALID_SIGNATURE, VALID_MESSAGE_BODY);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getMessage()).isEqualTo("消息处理成功");
        assertThat(response.getBody().getResultCount()).isEqualTo(1);
        assertThat(response.getBody().getResults()).hasSize(1);
        assertThat(response.getBody().getProcessingTimeMs()).isGreaterThanOrEqualTo(0);

        log.info("{}响应状态: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}处理结果: {}", LOG_ITEM, response.getBody().getMessage());
        log.info("{}处理时间: {}ms", LOG_ITEM, response.getBody().getProcessingTimeMs());

        log.info("{} 测试正常的webhook消息处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldRejectInvalidSignature() {
        log.info("{} 开始测试签名验证失败 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String invalidSignature = "INVALID_SIGNATURE";
        log.info("{}无效签名: {}", LOG_ITEM, invalidSignature);

        // Mock签名验证失败
        when(messageDispatcher.dispatch(anyString(), anyString()))
            .thenThrow(new WebhookProcessingException("消息签名验证失败"));

        // When
        var response = webhookController.handleCallback(invalidSignature, VALID_MESSAGE_BODY);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("消息签名验证失败");
        assertThat(response.getBody().getResultCount()).isEqualTo(0);

        log.info("{}响应状态: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}错误信息: {}", LOG_ITEM, response.getBody().getMessage());

        log.info("{} 测试签名验证失败完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleInvalidMessageFormat() {
        log.info("{} 开始测试消息格式错误 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String invalidMessageBody = "{ invalid json }";
        log.info("{}无效消息体: {}", LOG_ITEM, invalidMessageBody);

        // Mock消息解析失败
        when(messageDispatcher.dispatch(anyString(), anyString()))
            .thenThrow(new WebhookProcessingException("消息格式错误"));

        // When
        var response = webhookController.handleCallback(VALID_SIGNATURE, invalidMessageBody);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("消息格式错误");

        log.info("{}响应状态: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}错误信息: {}", LOG_ITEM, response.getBody().getMessage());

        log.info("{} 测试消息格式错误完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("success")
    void shouldHandleDuplicateMessage() {
        log.info("{} 开始测试重复消息处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        log.info("{}重复消息测试", LOG_ITEM);

        // Mock重复消息结果
        MessageResult duplicateResult = MessageResult.duplicate("139830976934");
        when(messageDispatcher.dispatch(anyString(), anyString()))
            .thenReturn(Arrays.asList(duplicateResult));

        // When
        var response = webhookController.handleCallback(VALID_SIGNATURE, VALID_MESSAGE_BODY);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getResultCount()).isEqualTo(1);

        log.info("{}响应状态: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}处理结果数量: {}", LOG_ITEM, response.getBody().getResultCount());

        log.info("{} 测试重复消息处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("error")
    void shouldHandleProcessingException() {
        log.info("{} 开始测试处理异常 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        log.info("{}模拟系统异常", LOG_ITEM);

        // Mock系统异常
        when(messageDispatcher.dispatch(anyString(), anyString()))
            .thenThrow(new RuntimeException("系统内部错误"));

        // When
        var response = webhookController.handleCallback(VALID_SIGNATURE, VALID_MESSAGE_BODY);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("系统异常");

        log.info("{}响应状态: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}错误信息: {}", LOG_ITEM, response.getBody().getMessage());

        log.info("{} 测试处理异常完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("success")
    void shouldReturnHealthStatus() {
        log.info("{} 开始测试健康检查 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // When
        var response = webhookController.health();

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo("Webhook service is running");

        log.info("{}健康检查响应: {}", LOG_ITEM, response.getBody());

        log.info("{} 测试健康检查完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("success")
    void shouldHandleEmptyResults() {
        log.info("{} 开始测试空结果处理 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        log.info("{}模拟无处理器场景", LOG_ITEM);

        // Mock空结果
        when(messageDispatcher.dispatch(anyString(), anyString()))
            .thenReturn(Collections.emptyList());

        // When
        var response = webhookController.handleCallback(VALID_SIGNATURE, VALID_MESSAGE_BODY);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getResultCount()).isEqualTo(0);
        assertThat(response.getBody().getResults()).isEmpty();

        log.info("{}响应状态: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}结果数量: {}", LOG_ITEM, response.getBody().getResultCount());

        log.info("{} 测试空结果处理完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
