/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.controller;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fulfillmen.support.alibaba.webhook.MessageDispatcher;
import com.fulfillmen.support.alibaba.webhook.MessageResult;
import com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException;

import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Webhook控制器 (测试用)
 * 接收阿里巴巴的回调消息
 * 
 * 注意：此类仅用于测试目的，不应在生产代码中使用
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/alibaba/webhook")
@RequiredArgsConstructor
public class WebhookController {

    private final MessageDispatcher messageDispatcher;

    /**
     * 接收阿里巴巴回调消息
     *
     * @param signature 签名头
     * @param requestBody 消息体
     * @return 处理结果
     */
    @PostMapping("/callback")
    public ResponseEntity<WebhookResponse> handleCallback(
            @RequestHeader(value = "signature", required = false) String signature,
            @RequestBody String requestBody) {

        long startTime = System.currentTimeMillis();

        try {
            log.info("收到阿里巴巴webhook回调，消息长度: {}", requestBody.length());

            // 处理消息
            List<MessageResult> results = messageDispatcher.dispatch(requestBody, signature);

            long duration = System.currentTimeMillis() - startTime;
            log.info("Webhook消息处理完成，处理时间: {}ms，处理结果数量: {}", duration, results.size());

            // 构建响应
            WebhookResponse response = WebhookResponse.builder()
                .success(true)
                .message("消息处理成功")
                .resultCount(results.size())
                .processingTimeMs(duration)
                .results(results)
                .build();

            return ResponseEntity.ok(response);

        } catch (WebhookProcessingException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Webhook消息处理失败，处理时间: {}ms", duration, e);

            WebhookResponse response = WebhookResponse.builder()
                .success(false)
                .message("消息处理失败: " + e.getMessage())
                .resultCount(0)
                .processingTimeMs(duration)
                .build();

            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Webhook消息处理异常，处理时间: {}ms", duration, e);

            WebhookResponse response = WebhookResponse.builder()
                .success(false)
                .message("系统异常: " + e.getMessage())
                .resultCount(0)
                .processingTimeMs(duration)
                .build();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 健康检查接口
     */
    @PostMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Webhook service is running");
    }

    /**
     * Webhook响应对象 (测试用)
     */
    @Data
    @Builder
    public static class WebhookResponse implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private boolean success;
        private String message;
        private int resultCount;
        private long processingTimeMs;
        private List<MessageResult> results;
    }
}
