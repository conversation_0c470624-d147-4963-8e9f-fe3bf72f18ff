# WebhookController 测试用例

本目录包含了针对阿里巴巴Webhook回调处理的完整测试用例。

## 测试文件说明

### 1. WebhookControllerMockTest.java
Mock测试类，使用模拟对象测试WebhookController的各种场景：

**测试用例：**
- `shouldHandleValidWebhookMessage()` - 测试正常的webhook消息处理
- `shouldRejectInvalidSignature()` - 测试签名验证失败场景
- `shouldHandleInvalidMessageFormat()` - 测试消息格式错误处理
- `shouldHandleDuplicateMessage()` - 测试重复消息处理
- `shouldHandleProcessingException()` - 测试系统异常处理
- `shouldReturnHealthStatus()` - 测试健康检查接口
- `shouldHandleEmptyResults()` - 测试空结果处理

### 2. WebhookControllerIntegrationTest.java
集成测试类，测试完整的HTTP请求处理流程：

**测试用例：**
- `shouldProcessWebhookEndToEnd()` - 端到端webhook处理测试
- `shouldReturnHealthStatusViaHttp()` - HTTP健康检查测试
- `shouldHandleInvalidJsonViaHttp()` - HTTP无效JSON处理测试
- `shouldHandleMissingSignatureHeader()` - 缺失签名头测试

## 测试数据

测试使用的消息数据基于真实的阿里巴巴订单成功回调：

```json
[{
    "bizKey": "2806599398122540788",
    "data": {
        "buyerMemberId": "b2b-2207416548807a4d12",
        "currentStatus": "success",
        "orderId": 2806599398122540788,
        "sellerMemberId": "b2b-221280776451649a09",
        "msgSendTime": "2025-07-10 17:55:46"
    },
    "gmtBorn": 1752141346107,
    "msgId": "139830976934",
    "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
    "userInfo": "b2b-2207416548807a4d12"
}]
```

**签名：** `38DF23A2911926BD925AA1D89DC2D282777BA572`

## 运行测试

### 运行Mock测试
```bash
mvn test -Dtest=WebhookControllerMockTest -Ptest -Dspotless.skip=true
```

### 运行集成测试
```bash
mvn test -Dtest=WebhookControllerIntegrationTest -Ptest -Dspotless.skip=true
```

### 运行所有Webhook测试
```bash
mvn test -Dtest="*Webhook*" -Ptest -Dspotless.skip=true
```

## 相关组件

测试涉及的主要组件：

1. **WebhookController** - Webhook接收控制器
2. **MessageDispatcher** - 消息分发器
3. **OrderSuccessMessageHandler** - 订单成功消息处理器
4. **OrderSuccessData** - 订单成功数据模型
5. **SignatureValidator** - 签名验证器
6. **MessageRouter** - 消息路由器
7. **IdempotentManager** - 幂等性管理器

## 测试覆盖场景

- ✅ 正常消息处理流程
- ✅ 签名验证（成功/失败）
- ✅ 消息格式验证
- ✅ 重复消息处理
- ✅ 异常处理机制
- ✅ 健康检查接口
- ✅ HTTP请求处理
- ✅ 空结果处理

## 注意事项

1. **测试环境配置**：集成测试需要配置Redis连接信息
2. **签名验证**：测试中使用空签名，实际环境需要配置正确的密钥
3. **消息格式**：测试数据使用JSON数组格式，符合阿里巴巴webhook规范
4. **异常处理**：测试覆盖了各种异常场景，确保系统稳定性

## 扩展测试

如需添加新的测试用例：

1. 在对应的测试类中添加新的测试方法
2. 使用 `@Tag` 注解标记测试类型（如 "success", "error", "integration"）
3. 遵循现有的日志格式和断言模式
4. 确保测试数据的真实性和完整性
