# Webhook 测试用例

本目录包含了针对阿里巴巴Webhook回调处理的测试用例。

**重要说明：** 本目录中的所有类仅用于测试目的，不应在生产代码中使用。

## 测试文件说明

### 1. 测试用业务类

#### data/OrderSuccessData.java
测试用的订单成功数据模型，用于验证数据序列化和反序列化。

### 2. 测试用例

#### OrderSuccessDataTest.java
数据模型测试类，测试OrderSuccessData的各种功能：

**测试用例：**
- `shouldCreateOrderSuccessDataWithAllFields()` - 测试字段设置
- `shouldSerializeToJson()` - 测试JSON序列化
- `shouldDeserializeFromJson()` - 测试JSON反序列化
- `shouldSerializeAndDeserializeCorrectly()` - 测试序列化一致性
- `shouldHandleNullValues()` - 测试空值处理
- `shouldHandlePartialData()` - 测试部分数据处理
- `shouldValidateOrderIdFormat()` - 测试订单ID格式验证

#### OrderSuccessMessageHandlerTest.java
消息处理器测试类，测试OrderSuccessMessageHandler的各种功能：

**测试用例：**
- `shouldSupportOrderSuccessMessageType()` - 测试支持的消息类型
- `shouldNotSupportOtherMessageTypes()` - 测试不支持的消息类型
- `shouldReturnCorrectPriority()` - 测试处理器优先级
- `shouldReturnCorrectDataClass()` - 测试数据类型
- `shouldHandleValidOrderSuccessMessage()` - 测试处理有效消息
- `shouldHandleMessageWithNullData()` - 测试处理空数据
- `shouldHandleMessageWithPartialData()` - 测试处理部分数据
- `shouldHandleMessageWithDifferentStatuses()` - 测试处理不同状态
- `shouldHandleMessageWithLargeOrderId()` - 测试处理大订单ID
- `shouldHandleMessageWithEmptyStrings()` - 测试处理空字符串
- `shouldProcessMessageQuickly()` - 测试处理性能

## 测试数据

测试使用的消息数据基于真实的阿里巴巴订单成功回调：

```json
[{
    "bizKey": "2806599398122540788",
    "data": {
        "buyerMemberId": "b2b-2207416548807a4d12",
        "currentStatus": "success",
        "orderId": 2806599398122540788,
        "sellerMemberId": "b2b-221280776451649a09",
        "msgSendTime": "2025-07-10 17:55:46"
    },
    "gmtBorn": 1752141346107,
    "msgId": "139830976934",
    "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
    "userInfo": "b2b-2207416548807a4d12"
}]
```

**签名：** `38DF23A2911926BD925AA1D89DC2D282777BA572`

## 运行测试

### 运行Mock测试
```bash
mvn test -Dtest=WebhookControllerMockTest -Ptest -Dspotless.skip=true
```

### 运行集成测试
```bash
mvn test -Dtest=WebhookControllerIntegrationTest -Ptest -Dspotless.skip=true
```

### 运行数据模型测试
```bash
mvn test -Dtest=OrderSuccessDataTest -Ptest -Dspotless.skip=true
```

### 运行消息处理器测试
```bash
mvn test -Dtest=OrderSuccessMessageHandlerTest -Ptest -Dspotless.skip=true
```

### 运行所有Webhook测试
```bash
mvn test -Dtest="*Webhook*" -Ptest -Dspotless.skip=true
```

## 相关组件

测试涉及的主要组件：

1. **WebhookController** - Webhook接收控制器
2. **MessageDispatcher** - 消息分发器
3. **OrderSuccessMessageHandler** - 订单成功消息处理器
4. **OrderSuccessData** - 订单成功数据模型
5. **SignatureValidator** - 签名验证器
6. **MessageRouter** - 消息路由器
7. **IdempotentManager** - 幂等性管理器

## 测试覆盖场景

- ✅ 正常消息处理流程
- ✅ 签名验证（成功/失败）
- ✅ 消息格式验证
- ✅ 重复消息处理
- ✅ 异常处理机制
- ✅ 健康检查接口
- ✅ HTTP请求处理
- ✅ 空结果处理

## 注意事项

1. **测试环境配置**：集成测试需要配置Redis连接信息
2. **签名验证**：测试中使用空签名，实际环境需要配置正确的密钥
3. **消息格式**：测试数据使用JSON数组格式，符合阿里巴巴webhook规范
4. **异常处理**：测试覆盖了各种异常场景，确保系统稳定性

## 扩展测试

如需添加新的测试用例：

1. 在对应的测试类中添加新的测试方法
2. 使用 `@Tag` 注解标记测试类型（如 "success", "error", "integration"）
3. 遵循现有的日志格式和断言模式
4. 确保测试数据的真实性和完整性
