# Webhook 测试用例

本目录包含了针对阿里巴巴Webhook回调处理的测试用例。

**重要说明：** 本目录中的所有类仅用于测试目的，不应在生产代码中使用。

## 测试文件说明

### 1. 测试用业务类

#### data/OrderSuccessData.java
测试用的订单成功数据模型，用于验证数据序列化和反序列化。

### 2. 测试用例

#### OrderSuccessDataTest.java
数据模型测试类，测试OrderSuccessData的各种功能：

**测试用例：**
- `shouldCreateOrderSuccessDataWithAllFields()` - 测试字段设置
- `shouldSerializeToJson()` - 测试JSON序列化
- `shouldDeserializeFromJson()` - 测试JSON反序列化
- `shouldSerializeAndDeserializeCorrectly()` - 测试序列化一致性
- `shouldHandleNullValues()` - 测试空值处理
- `shouldHandlePartialData()` - 测试部分数据处理
- `shouldValidateOrderIdFormat()` - 测试订单ID格式验证
- `shouldValidateStatusValues()` - 测试状态值验证

## 测试数据

测试使用的消息数据基于真实的阿里巴巴订单成功回调：

```json
[{
    "bizKey": "2806599398122540788",
    "data": {
        "buyerMemberId": "b2b-2207416548807a4d12",
        "currentStatus": "success",
        "orderId": 2806599398122540788,
        "sellerMemberId": "b2b-221280776451649a09",
        "msgSendTime": "2025-07-10 17:55:46"
    },
    "gmtBorn": 1752141346107,
    "msgId": "139830976934",
    "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
    "userInfo": "b2b-2207416548807a4d12"
}]
```

**签名：** `38DF23A2911926BD925AA1D89DC2D282777BA572`

## 运行测试

### 运行Mock测试
```bash
mvn test -Dtest=WebhookControllerMockTest -Ptest -Dspotless.skip=true
```

### 运行数据模型测试
```bash
mvn test -Dtest=OrderSuccessDataTest -Ptest -Dspotless.skip=true
```

### 运行所有Webhook测试
```bash
mvn test -Dtest="*OrderSuccess*" -Ptest -Dspotless.skip=true
```

## 相关组件

测试涉及的主要组件：

1. **OrderSuccessData** - 订单成功数据模型

## 测试覆盖场景

- ✅ 数据模型字段设置和获取
- ✅ JSON序列化和反序列化
- ✅ 空值和部分数据处理
- ✅ 数据验证和格式检查

## 注意事项

1. **测试目的**：这些类仅用于测试目的，不应在生产代码中使用
2. **消息格式**：测试数据基于真实的阿里巴巴webhook消息格式
3. **数据完整性**：测试覆盖了各种数据场景，确保数据处理的稳定性

## 扩展测试

如需添加新的测试用例：

1. 在OrderSuccessDataTest中添加新的测试方法
2. 使用 `@Tag` 注解标记测试类型（如 "data", "serialization", "validation"）
3. 遵循现有的日志格式和断言模式
4. 确保测试数据的真实性和完整性
