/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import com.fulfillmen.support.alibaba.api.BaseAPITest;
import com.fulfillmen.support.alibaba.webhook.controller.WebhookController;

import lombok.extern.slf4j.Slf4j;

/**
 * WebhookController 集成测试
 * 测试完整的HTTP请求处理流程
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
    "alibaba.open1688.secret-key=test-secret-key",
    "spring.redis.host=localhost",
    "spring.redis.port=6379"
})
public class WebhookControllerIntegrationTest extends BaseAPITest {

    private static final String LOG_SEPARATOR = "====================";
    private static final String LOG_ITEM = "- ";

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    /**
     * 测试用例的消息数据 - 基于用户提供的真实数据
     */
    private static final String VALID_MESSAGE_BODY = """
        [{
            "bizKey": "2806599398122540788",
            "data": {
                "buyerMemberId": "b2b-2207416548807a4d12",
                "currentStatus": "success",
                "orderId": 2806599398122540788,
                "sellerMemberId": "b2b-221280776451649a09",
                "msgSendTime": "2025-07-10 17:55:46"
            },
            "gmtBorn": 1752141346107,
            "msgId": "139830976934",
            "type": "ORDER_BUYER_VIEW_ORDER_SUCCESS",
            "userInfo": "b2b-2207416548807a4d12"
        }]
        """;

    @Test
    @Tag("integration")
    void shouldProcessWebhookEndToEnd() {
        log.info("{} 开始端到端webhook测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String url = "http://localhost:" + port + "/api/alibaba/webhook/callback";
        log.info("{}测试URL: {}", LOG_ITEM, url);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 注意：这里使用空签名，因为测试环境可能没有配置正确的密钥
        headers.set("signature", "");

        HttpEntity<String> request = new HttpEntity<>(VALID_MESSAGE_BODY, headers);

        long startTime = System.currentTimeMillis();

        // When
        ResponseEntity<WebhookController.WebhookResponse> response = restTemplate.exchange(
            url, HttpMethod.POST, request, WebhookController.WebhookResponse.class);

        long duration = System.currentTimeMillis() - startTime;

        // Then
        assertThat(response).isNotNull();
        log.info("{}HTTP状态码: {}", LOG_ITEM, response.getStatusCode());
        log.info("{}请求耗时: {}ms", LOG_ITEM, duration);

        if (response.getStatusCode() == HttpStatus.OK) {
            assertThat(response.getBody()).isNotNull();
            assertThat(response.getBody().isSuccess()).isTrue();
            log.info("{}处理成功: {}", LOG_ITEM, response.getBody().getMessage());
            log.info("{}处理结果数量: {}", LOG_ITEM, response.getBody().getResultCount());
        } else {
            // 如果失败，记录错误信息用于调试
            log.warn("{}请求失败: {}", LOG_ITEM, response.getBody());
        }

        log.info("{} 端到端webhook测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("integration")
    void shouldReturnHealthStatusViaHttp() {
        log.info("{} 开始HTTP健康检查测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String url = "http://localhost:" + port + "/api/alibaba/webhook/health";
        log.info("{}健康检查URL: {}", LOG_ITEM, url);

        // When
        ResponseEntity<String> response = restTemplate.postForEntity(url, null, String.class);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isEqualTo("Webhook service is running");

        log.info("{}健康检查响应: {}", LOG_ITEM, response.getBody());

        log.info("{} HTTP健康检查测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("integration")
    void shouldHandleInvalidJsonViaHttp() {
        log.info("{} 开始HTTP无效JSON测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String url = "http://localhost:" + port + "/api/alibaba/webhook/callback";
        String invalidJson = "{ invalid json }";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("signature", "");

        HttpEntity<String> request = new HttpEntity<>(invalidJson, headers);

        // When
        ResponseEntity<WebhookController.WebhookResponse> response = restTemplate.exchange(
            url, HttpMethod.POST, request, WebhookController.WebhookResponse.class);

        // Then
        assertThat(response).isNotNull();
        log.info("{}HTTP状态码: {}", LOG_ITEM, response.getStatusCode());

        // 应该返回错误状态
        if (response.getBody() != null) {
            log.info("{}错误信息: {}", LOG_ITEM, response.getBody().getMessage());
        }

        log.info("{} HTTP无效JSON测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }

    @Test
    @Tag("integration")
    void shouldHandleMissingSignatureHeader() {
        log.info("{} 开始缺失签名头测试 {}", LOG_SEPARATOR, LOG_SEPARATOR);

        // Given
        String url = "http://localhost:" + port + "/api/alibaba/webhook/callback";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 不设置signature头

        HttpEntity<String> request = new HttpEntity<>(VALID_MESSAGE_BODY, headers);

        // When
        ResponseEntity<WebhookController.WebhookResponse> response = restTemplate.exchange(
            url, HttpMethod.POST, request, WebhookController.WebhookResponse.class);

        // Then
        assertThat(response).isNotNull();
        log.info("{}HTTP状态码: {}", LOG_ITEM, response.getStatusCode());

        if (response.getBody() != null) {
            log.info("{}响应消息: {}", LOG_ITEM, response.getBody().getMessage());
        }

        log.info("{} 缺失签名头测试完成 {}", LOG_SEPARATOR, LOG_SEPARATOR);
    }
}
