/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.data;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 订单成功消息数据模型 (测试用)
 * 对应 ORDER_BUYER_VIEW_ORDER_SUCCESS 消息类型
 * 
 * 注意：此类仅用于测试目的，不应在生产代码中使用
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Data
public class OrderSuccessData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 买家会员ID
     */
    private String buyerMemberId;

    /**
     * 当前订单状态
     */
    private String currentStatus;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 卖家会员ID
     */
    private String sellerMemberId;

    /**
     * 消息发送时间
     */
    private String msgSendTime;
}
