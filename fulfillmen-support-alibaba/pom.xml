<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.support</groupId>
        <artifactId>fulfillmen-support</artifactId>
        <version>1.1.7-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>fulfillmen-support-alibaba</artifactId>
    <name>Fulfillmen Support Alibaba SDK ${project.version}</name>
    <description>Fulfillmen Shop Alibaba Support SDK</description>

    <dependencies>
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 阿里巴巴 Ocean 客户端 - 改为正常依赖 -->
        <!-- <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>ocean.client.java.biz</artifactId>
            <version>1.0.7</version>
        </dependency> -->
        
    </dependencies>

    <build>
        <plugins>
            <!-- 安装本地jar到仓库的插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>3.1.1</version>
                <executions>
                    <execution>
                        <id>install-ocean-client</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>install-file</goal>
                        </goals>
                        <configuration>
                            <file>${project.basedir}/src/main/libs/ocean.client.java.biz.jar</file>
                            <groupId>com.alibaba.platform.shared</groupId>
                            <artifactId>ocean.client.java.biz</artifactId>
                            <version>1.0.7</version>
                            <packaging>jar</packaging>
                            <generatePom>false</generatePom>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
