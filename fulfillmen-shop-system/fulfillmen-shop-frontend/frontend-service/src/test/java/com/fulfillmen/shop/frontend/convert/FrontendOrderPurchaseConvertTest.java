/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import static org.junit.jupiter.api.Assertions.*;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * 前端订单转换器测试类
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description 测试订单转换器的功能
 * @since 1.0.0
 */
class FrontendOrderPurchaseConvertTest {

    @Test
    void testConvertToUserPurchaseOrderListVO() {
        // 准备测试数据
        TzOrderPurchase orderPurchase = TzOrderPurchase.builder()
            .id(1L)
            .purchaseOrderNo("PO202507070001")
            .orderStatus(TzOrderPurchaseStatusEnum.PAYMENT_PENDING)
            .totalAmount(new BigDecimal("1000.00"))
            .exchangeRate(new BigDecimal("0.14")) // 1 CNY = 0.14 USD
            .lineItemCount(2)
            .gmtCreated(LocalDateTime.now())
            .build();

        // 执行转换
        UserPurchaseOrderListVO result = FrontendOrderPurchaseConvert.INSTANCE.convertToUserPurchaseOrderListVO(orderPurchase);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("PO202507070001", result.getOrderNo());
        assertEquals(TzOrderPurchaseStatusEnum.PAYMENT_PENDING, result.getOrderStatus());
        assertEquals("待支付", result.getOrderStatusText());
        assertEquals(new BigDecimal("1000.00"), result.getTotalAmount());
        assertEquals(2, result.getProductTypeCount());
        assertNotNull(result.getCreateTime());

        // 验证美元金额计算
        assertNotNull(result.getTotalAmountUsd());
        assertEquals(new BigDecimal("140.00"), result.getTotalAmountUsd()); // 1000 * 0.14 = 140.00

        // 验证业务逻辑字段
        assertTrue(result.getCanCancel()); // 待支付状态可以取消
        assertFalse(result.getCanConfirmReceipt()); // 当前业务流程不支持确认收货
        assertFalse(result.getCanApplyRefund()); // 待支付状态不能申请退款（需支付完成后才能退款）
    }

    @Test
    void testConvertToOrderItemInfo() {
        // 准备测试数据
        AttrJson attr1 = AttrJson.builder()
            .attrKey("颜色")
            .attrValue("红色")
            .build();

        AttrJson attr2 = AttrJson.builder()
            .attrKey("尺寸")
            .attrValue("L")
            .build();

        TzOrderItem orderItem = TzOrderItem.builder()
            .id(1L)
            .productTitle("测试商品")
            .productTitleEn("Test Product")
            .productImageUrl("https://example.com/image.jpg")
            .skuSpecs(Arrays.asList(attr1, attr2))
            .quantity(5)
            .price(new BigDecimal("200.00"))
            .build();

        // 执行转换
        UserPurchaseOrderListVO.OrderItemInfo result = FrontendOrderPurchaseConvert.INSTANCE.convertToOrderItemInfo(orderItem);

        // 验证结果
        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("测试商品", result.getProductTitle());
        assertEquals("Test Product", result.getProductTitleEn());
        assertEquals("https://example.com/image.jpg", result.getProductImage());
        assertEquals(2, result.getSkuSpecs().size());
        assertEquals(5, result.getOrderedQuantity());
        assertEquals(new BigDecimal("200.00"), result.getUnitPrice());
    }

    @Test
    void testCanCancelOrder() {
        // 测试可以取消的状态 - 根据 TzOrderPurchaseStatusEnum.isCancellable() 实现
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.PAYMENT_PENDING));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED));

        // 测试不能取消的状态 - 只有完成和已取消状态不能取消
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.IN_STOCK));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(TzOrderPurchaseStatusEnum.ORDER_CANCELLED));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canCancelOrder(null));
    }

    @Test
    void testCanConfirmReceipt() {
        // 根据 OrderStatusUtil.canConfirmReceipt() 实现，当前业务流程只到 WMS 入库，
        // 没有发货给客户的状态，所以所有状态都不能确认收货
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canConfirmReceipt(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canConfirmReceipt(TzOrderPurchaseStatusEnum.PAYMENT_PENDING));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canConfirmReceipt(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canConfirmReceipt(TzOrderPurchaseStatusEnum.IN_STOCK));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canConfirmReceipt(null));
    }

    @Test
    void testCanApplyRefund() {
        // 测试可以申请退款的状态 - 根据 OrderStatusUtil.canApplyRefund() 实现
        // 支付完成后到完成前的状态都可以申请退款
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED));
        assertTrue(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.IN_STOCK));

        // 测试不能申请退款的状态
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.TEMPORARILY_SAVED));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.PAYMENT_PENDING));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(TzOrderPurchaseStatusEnum.ORDER_CANCELLED));
        assertFalse(FrontendOrderPurchaseConvert.INSTANCE.canApplyRefund(null));
    }

    @Test
    void testConvertToOrderItemInfoList() {
        // 准备测试数据
        TzOrderItem item1 = TzOrderItem.builder()
            .id(1L)
            .productTitle("商品1")
            .productImageUrl("https://example.com/image1.jpg")
            .quantity(2)
            .price(new BigDecimal("100.00"))
            .build();

        TzOrderItem item2 = TzOrderItem.builder()
            .id(2L)
            .productTitle("商品2")
            .productImageUrl("https://example.com/image2.jpg")
            .quantity(3)
            .price(new BigDecimal("200.00"))
            .build();

        List<TzOrderItem> orderItems = Arrays.asList(item1, item2);

        // 执行转换
        List<UserPurchaseOrderListVO.OrderItemInfo> result = FrontendOrderPurchaseConvert.INSTANCE.convertToOrderItemInfoList(orderItems);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        UserPurchaseOrderListVO.OrderItemInfo resultItem1 = result.get(0);
        assertEquals("1", resultItem1.getId());
        assertEquals("商品1", resultItem1.getProductTitle());
        assertEquals(2, resultItem1.getOrderedQuantity());

        UserPurchaseOrderListVO.OrderItemInfo resultItem2 = result.get(1);
        assertEquals("2", resultItem2.getId());
        assertEquals("商品2", resultItem2.getProductTitle());
        assertEquals(3, resultItem2.getOrderedQuantity());
    }

    @Test
    void testCalculateTotalAmountUsd() {
        // 测试使用订单汇率计算美元金额
        BigDecimal totalAmount = new BigDecimal("1000.00");
        BigDecimal exchangeRate = new BigDecimal("0.14");

        BigDecimal result = FrontendOrderPurchaseConvert.INSTANCE.calculateTotalAmountUsd(totalAmount, exchangeRate);

        assertNotNull(result);
        assertEquals(new BigDecimal("140.00"), result);
    }

    @Test
    void testCalculateItemUsdPriceWithExchangeRate() {
        // 测试带汇率的订单项转换
        TzOrderItem orderItem = TzOrderItem.builder()
            .id(1L)
            .productTitle("测试商品")
            .productTitleEn("Test Product")
            .productImageUrl("https://example.com/image.jpg")
            .quantity(5)
            .price(new BigDecimal("200.00"))
            .build();

        BigDecimal exchangeRate = new BigDecimal("0.14");

        // 执行转换
        UserPurchaseOrderListVO.OrderItemInfo result = FrontendOrderPurchaseConvert.INSTANCE
            .convertToOrderItemInfoWithExchangeRate(orderItem, exchangeRate);

        // 验证结果
        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("测试商品", result.getProductTitle());
        assertEquals("Test Product", result.getProductTitleEn());
        assertEquals(5, result.getOrderedQuantity());
        assertEquals(new BigDecimal("200.00"), result.getUnitPrice());

        // 验证美元价格计算
        assertNotNull(result.getUnitPriceUsd());
        assertEquals(new BigDecimal("28.00"), result.getUnitPriceUsd()); // 200 * 0.14 = 28.00
    }
}
