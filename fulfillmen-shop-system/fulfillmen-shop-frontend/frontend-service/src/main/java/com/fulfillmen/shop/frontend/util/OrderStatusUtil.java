/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.util;

import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;

/**
 * 订单状态工具类
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 处理订单状态转换和权限判断
 */
public class OrderStatusUtil {

    /**
     * 订单状态信息
     */
    public static class OrderStatusInfo {

        private final String displayName;
        private final String description;
        private final String icon;
        private final String color;
        private final String tagType;
        private final int progressPercentage;

        public OrderStatusInfo(String displayName, String description, String icon, String color, String tagType, int progressPercentage) {
            this.displayName = displayName;
            this.description = description;
            this.icon = icon;
            this.color = color;
            this.tagType = tagType;
            this.progressPercentage = progressPercentage;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }

        public String getIcon() {
            return icon;
        }

        public String getColor() {
            return color;
        }

        public String getTagType() {
            return tagType;
        }

        public int getProgressPercentage() {
            return progressPercentage;
        }
    }

    /**
     * 获取订单状态完整信息
     */
    public static OrderStatusInfo getOrderStatusInfo(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return new OrderStatusInfo("未知状态", "订单状态异常", "question-circle", "#d9d9d9", "info", 0);
        }

        return switch (status) {
            case TEMPORARILY_SAVED -> new OrderStatusInfo(
                "订单保存中",
                "订单已保存，等待进一步处理。可能是询价商品需要代付款或余额不足",
                "save",
                "#faad14",
                "warning",
                5
            );
            case PAYMENT_PENDING -> new OrderStatusInfo(
                "待支付",
                "订单已确认，请尽快完成支付。支持余额支付、Stripe支付等多种方式",
                "credit-card",
                "#ff7a45",
                "warning",
                10
            );
            case PAYMENT_COMPLETED -> new OrderStatusInfo(
                "支付成功",
                "支付成功，我们正在为您准备商品。订单已进入内部处理流程",
                "check-circle",
                "#52c41a",
                "success",
                20
            );
            case PROCUREMENT_IN_PROGRESS -> new OrderStatusInfo(
                "采购中",
                "采购员正在处理您的订单，正在向供应商下单采购商品",
                "shopping-cart",
                "#1890ff",
                "info",
                40
            );
            case PARTIALLY_FULFILLED -> new OrderStatusInfo(
                "部分履约",
                "部分供应商订单已完成，其他订单仍在处理中。您可以查看各商品的详细进展",
                "clock-circle",
                "#722ed1",
                "primary",
                60
            );
            case SUPPLIER_SHIPPED -> new OrderStatusInfo(
                "商品发货中",
                "供应商已将商品发往我们的仓库，商品正在运输途中",
                "truck",
                "#13c2c2",
                "info",
                70
            );
            case WAREHOUSE_PENDING_RECEIVED -> new OrderStatusInfo(
                "运输中",
                "商品正在运输途中，等待我们的仓库接收",
                "loading",
                "#2f54eb",
                "primary",
                80
            );
            case WAREHOUSE_RECEIVED -> new OrderStatusInfo(
                "已到仓库",
                "商品已到达仓库，正在进行质检和上架流程",
                "home",
                "#389e0d",
                "success",
                90
            );
            case IN_STOCK -> new OrderStatusInfo(
                "已完成",
                "订单已完成，商品已通过质检并成功入库。感谢您的购买！",
                "trophy",
                "#52c41a",
                "success",
                100
            );
            case ORDER_CANCELLED -> new OrderStatusInfo(
                "已取消",
                "订单已取消。如有疑问请联系客服",
                "close-circle",
                "#ff4d4f",
                "danger",
                0
            );
            default -> new OrderStatusInfo(
                status.getDescription(),
                "订单状态：" + status.getDescription(),
                "info-circle",
                "#d9d9d9",
                "info",
                0
            );
        };
    }

    /**
     * 获取用户友好的状态显示名称
     */
    public static String getUserFriendlyStatusName(TzOrderPurchaseStatusEnum status) {
        return getOrderStatusInfo(status).getDisplayName();
    }

    /**
     * 获取用户友好的状态描述
     */
    public static String getUserFriendlyStatusDescription(TzOrderPurchaseStatusEnum status) {
        return getOrderStatusInfo(status).getDescription();
    }

    /**
     * 获取状态图标
     */
    public static String getStatusIcon(TzOrderPurchaseStatusEnum status) {
        return getOrderStatusInfo(status).getIcon();
    }

    /**
     * 获取状态颜色
     */
    public static String getStatusColor(TzOrderPurchaseStatusEnum status) {
        return getOrderStatusInfo(status).getColor();
    }

    /**
     * 获取状态标签类型
     */
    public static String getStatusTagType(TzOrderPurchaseStatusEnum status) {
        return getOrderStatusInfo(status).getTagType();
    }

    /**
     * 获取订单进度百分比
     */
    public static int getProgressPercentage(TzOrderPurchaseStatusEnum status) {
        return getOrderStatusInfo(status).getProgressPercentage();
    }

    /**
     * 判断订单是否可以取消
     */
    public static boolean canCancel(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isCancellable();
    }

    /**
     * 判断订单是否可以支付
     */
    public static boolean canPay(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isPayable();
    }

    /**
     * 判断订单是否可以确认收货
     * 注意：这个方法适用于从WMS发货给客户后的确认收货
     * 现在的状态枚举是到WMS入库为止，所以这里返回false
     * 后续如果有发货给客户的状态，需要相应调整
     */
    public static boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        // 当前状态枚举只到WMS入库，没有发货给客户的状态
        // 如果后续需要客户确认收货功能，需要扩展状态枚举
        return false;
    }

    /**
     * 判断订单是否可以申请退款
     */
    public static boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 支付完成后到完成前的状态都可以申请退款
        return status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED ||
            status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 判断订单是否可以查看物流
     */
    public static boolean canViewTracking(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 采购中及以后的状态可以查看物流
        return status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.PARTIALLY_FULFILLED ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 判断订单是否为最终状态
     */
    public static boolean isFinalStatus(TzOrderPurchaseStatusEnum status) {
        return status == TzOrderPurchaseStatusEnum.IN_STOCK ||
            status == TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
    }

    /**
     * 判断订单是否为进行中状态
     */
    public static boolean isInProgress(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isInProgress();
    }

    /**
     * 获取状态对应的Element Plus Tag类型
     */
    public static String getElementTagType(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return "";
        }

        return switch (status) {
            case TEMPORARILY_SAVED, PAYMENT_PENDING -> "warning";
            case PAYMENT_COMPLETED -> "success";
            case PROCUREMENT_IN_PROGRESS, WAREHOUSE_PENDING_RECEIVED -> "info";
            case PARTIALLY_FULFILLED, SUPPLIER_SHIPPED -> "primary";
            case WAREHOUSE_RECEIVED, IN_STOCK -> "success";
            case ORDER_CANCELLED -> "danger";
            default -> "info";
        };
    }
}
