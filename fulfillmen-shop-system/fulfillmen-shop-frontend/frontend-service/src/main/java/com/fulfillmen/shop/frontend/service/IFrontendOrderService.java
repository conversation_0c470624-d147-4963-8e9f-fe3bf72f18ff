/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service;

import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.req.OrderReq.CancelOrderRequest;
import com.fulfillmen.shop.domain.req.OrderReq.ConfirmReceiptRequest;
import com.fulfillmen.shop.domain.req.OrderReq.CreateOrderSubmitReq;
import com.fulfillmen.shop.domain.req.OrderReq.OrderPreviewReq;
import com.fulfillmen.shop.domain.req.OrderReq.RefundApplicationRequest;
import com.fulfillmen.shop.domain.req.OrderReq.RefundApplicationVO;
import com.fulfillmen.shop.frontend.vo.OrderPreviewVO;
import com.fulfillmen.shop.frontend.vo.OrderSubmitVO;
import com.fulfillmen.shop.frontend.vo.UserOrderDetailVO;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO;

/**
 * 前端订单服务接口
 * <pre>
 * 包含所有用户端订单相关的操作：
 * 1. 订单预览和创建
 * 2. 订单列表和详情查询
 * 3. 订单状态管理（取消、确认收货）
 * 4. 退款申请
 * 5. 物流追踪
 * </pre>
 *
 * @author: <EMAIL>
 * @date: 2025/6/23
 * @description: 前端订单服务接口
 */
public interface IFrontendOrderService {

    /**
     * 订单预览
     *
     * @param orderPreviewReq 订单预览请求
     * @return 订单预览结果
     */
    OrderPreviewVO previewOrder(OrderPreviewReq orderPreviewReq);

    /**
     * 提交订单
     *
     * @param orderSubmitReq 订单提交请求
     * @return 订单提交结果
     */
    OrderSubmitVO submitOrder(CreateOrderSubmitReq orderSubmitReq);

    /**
     * 根据令牌查询预览信息
     *
     * @param idempotentToken 幂等令牌
     * @return 订单预览结果
     */
    OrderPreviewVO getPreviewByToken(String idempotentToken);

    /**
     * 分页查询用户订单列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param status  订单状态（可选）
     * @param keyword 搜索关键词（可选）
     * @return 分页订单列表
     */
    PageDTO<UserPurchaseOrderListVO> getOrderList(int page, int size, Integer status, String keyword);

    /**
     * 获取订单详情
     *
     * @param orderNo 订单号
     * @return 订单详情
     */
    UserOrderDetailVO getOrderDetail(String orderNo);

    /**
     * 获取订单物流追踪信息
     *
     * @param orderNo 订单号
     * @return 物流追踪信息
     */
    UserOrderDetailVO.TrackingInfo getOrderTracking(String orderNo);

    /**
     * 确认收货
     *
     * @param orderNo 订单号
     * @param request 确认收货请求
     */
    void confirmReceipt(String orderNo, ConfirmReceiptRequest request);

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @param request 取消订单请求
     */
    void cancelOrder(String orderNo, CancelOrderRequest request);

    /**
     * 申请退款
     *
     * @param orderNo 订单号
     * @param request 退款申请请求
     * @return 退款申请结果
     */
    RefundApplicationVO applyRefund(String orderNo, RefundApplicationRequest request);

}
