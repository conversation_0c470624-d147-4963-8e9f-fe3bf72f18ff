/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fulfillmen.shop.domain.dto.PageDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.frontend.util.OrderStatusUtil;
import com.fulfillmen.shop.frontend.vo.UserOrderDetailVO;
import com.fulfillmen.shop.frontend.vo.UserPurchaseOrderListVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 前端采购订单转换器
 *
 * <AUTHOR>
 * @date 2025/7/7 16:16
 * @description 处理采购订单与前端VO之间的转换
 */
@Mapper
public interface FrontendOrderPurchaseConvert {

    FrontendOrderPurchaseConvert INSTANCE = Mappers.getMapper(FrontendOrderPurchaseConvert.class);

    /**
     * 转换采购订单分页对象为前端订单列表分页对象
     *
     * @param orderPurchasePage 采购订单分页对象
     * @return 前端订单列表分页对象
     */
    @Mappings({
        @Mapping(source = "current", target = "pageIndex"),
        @Mapping(source = "size", target = "pageSize")
    })
    PageDTO<UserPurchaseOrderListVO> convertToUserPurchaseOrderListVO(Page<TzOrderPurchase> orderPurchasePage);

    /**
     * 转换订单项为前端订单项信息
     *
     * @param orderItem 订单项
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))"),
        @Mapping(target = "productId", source = "productSpuId"),
        @Mapping(target = "skuId", source = "productSkuId"),
        @Mapping(target = "productTitle", source = "productTitle"),
        @Mapping(target = "productTitleEn", source = "productTitleEn"),
        @Mapping(target = "skuSpecs", source = "skuSpecs"),
        @Mapping(target = "orderedQuantity", source = "quantity"),
        @Mapping(target = "unitOfMeasure", source = "unit"),
        @Mapping(target = "unitPrice", source = "price"),
        @Mapping(target = "unitPriceUsd", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), null))"),
        @Mapping(target = "lineTotalAmount", source = "lineTotalAmount"),
        @Mapping(target = "lineTotalAmountUsd", expression = "java(calculateItemUsdPrice(orderItem.getLineTotalAmount(), null))"),
        @Mapping(target = "itemStatus", source = "status"),
        @Mapping(target = "itemStatusName", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().name() : \"\")"),
        @Mapping(target = "available", expression = "java(true)"),
        @Mapping(target = "message", expression = "java(\"\")")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfo(TzOrderItem orderItem);

    /**
     * 转换订单项为前端订单项信息（带汇率）
     *
     * @param orderItem    订单项
     * @param exchangeRate 汇率
     * @return 前端订单项信息
     */
    @Mappings({
        @Mapping(target = "productImage", source = "orderItem.productImageUrl"),
        @Mapping(target = "id", expression = "java(String.valueOf(orderItem.getId()))"),
        @Mapping(target = "productId", source = "orderItem.productSpuId"),
        @Mapping(target = "skuId", source = "orderItem.productSkuId"),
        @Mapping(target = "productTitle", source = "orderItem.productTitle"),
        @Mapping(target = "productTitleEn", source = "orderItem.productTitleEn"),
        @Mapping(target = "skuSpecs", source = "orderItem.skuSpecs"),
        @Mapping(target = "orderedQuantity", source = "orderItem.quantity"),
        @Mapping(target = "unitOfMeasure", source = "orderItem.unit"),
        @Mapping(target = "unitPrice", source = "orderItem.price"),
        @Mapping(target = "unitPriceUsd", expression = "java(calculateItemUsdPrice(orderItem.getPrice(), exchangeRate))"),
        @Mapping(target = "lineTotalAmount", source = "orderItem.lineTotalAmount"),
        @Mapping(target = "lineTotalAmountUsd", expression = "java(calculateItemUsdPrice(orderItem.getLineTotalAmount(), exchangeRate))"),
        @Mapping(target = "itemStatus", source = "orderItem.status"),
        @Mapping(target = "itemStatusName", expression = "java(orderItem.getStatus() != null ? orderItem.getStatus().name() : \"\")"),
        @Mapping(target = "available", expression = "java(true)"),
        @Mapping(target = "message", expression = "java(\"\")")
    })
    UserPurchaseOrderListVO.OrderItemInfo convertToOrderItemInfoWithExchangeRate(TzOrderItem orderItem, BigDecimal exchangeRate);

    /**
     * 转换采购订单为前端订单详情
     *
     * @param tzOrderPurchase 采购订单
     * @return 前端订单详情
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "purchaseOrderNo"),
        @Mapping(target = "orderStatus", source = "orderStatus"),
        @Mapping(target = "orderStatusText", expression = "java(getOrderStatusText(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusDescription", expression = "java(getOrderStatusDescription(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusIcon", expression = "java(getOrderStatusIcon(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusColor", expression = "java(getOrderStatusColor(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderStatusTagType", expression = "java(getOrderStatusTagType(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "progressPercentage", expression = "java(getProgressPercentage(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "totalQuantity", source = "totalQuantity"),
        @Mapping(target = "productTypeCount", source = "lineItemCount"),
        @Mapping(target = "createTime", source = "gmtCreated"),
        @Mapping(target = "updateTime", source = "gmtModified"),
        @Mapping(target = "payTime", source = "paymentDate"),
        @Mapping(target = "deliveryTime", source = "orderDate"),
        @Mapping(target = "completedTime", source = "completionDate"),
        @Mapping(target = "totalAmountUsd", expression = "java(calculateTotalAmountUsd(tzOrderPurchase.getTotalAmount(), tzOrderPurchase.getExchangeRate()))"),
        @Mapping(target = "currency", expression = "java(\"CNY\")"),
        @Mapping(target = "buyerMessage", source = "purchaseNotes"),
        @Mapping(target = "mainProductTitle", expression = "java(\"\")"),
        @Mapping(target = "mainProductTitleEn", expression = "java(\"\")"),
        @Mapping(target = "mainProductImageUrl", expression = "java(\"\")"),
        @Mapping(target = "canCancel", expression = "java(canCancelOrder(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canPay", expression = "java(canPayOrder(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canConfirmReceipt", expression = "java(canConfirmReceipt(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canApplyRefund", expression = "java(canApplyRefund(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "canViewTracking", expression = "java(canViewTracking(tzOrderPurchase.getOrderStatus()))"),
        @Mapping(target = "orderItems", ignore = true)
    })
    UserPurchaseOrderListVO convertToUserPurchaseOrderListVO(TzOrderPurchase tzOrderPurchase);

    /**
     * 转换订单项列表为前端订单项列表
     *
     * @param tzOrderItems 订单项列表
     * @return 前端订单项列表
     */
    List<UserPurchaseOrderListVO.OrderItemInfo> convertToOrderItemInfoList(List<TzOrderItem> tzOrderItems);

    // ==================== 状态相关方法 ====================

    /**
     * 获取订单状态显示文本
     */
    default String getOrderStatusText(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getUserFriendlyStatusName(status);
    }

    /**
     * 获取订单状态描述
     */
    default String getOrderStatusDescription(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getUserFriendlyStatusDescription(status);
    }

    /**
     * 获取订单状态图标
     */
    default String getOrderStatusIcon(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getStatusIcon(status);
    }

    /**
     * 获取订单状态颜色
     */
    default String getOrderStatusColor(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getStatusColor(status);
    }

    /**
     * 获取订单状态标签类型
     */
    default String getOrderStatusTagType(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getElementTagType(status);
    }

    /**
     * 获取订单进度百分比
     */
    default Integer getProgressPercentage(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.getProgressPercentage(status);
    }

    // ==================== 权限判断方法 ====================

    /**
     * 判断订单是否可以取消
     */
    default Boolean canCancelOrder(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canCancel(status);
    }

    /**
     * 判断订单是否可以支付
     */
    default Boolean canPayOrder(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canPay(status);
    }

    /**
     * 判断订单是否可以确认收货
     */
    default Boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canConfirmReceipt(status);
    }

    /**
     * 判断订单是否可以申请退款
     */
    default Boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canApplyRefund(status);
    }

    /**
     * 判断订单是否可以查看物流
     */
    default Boolean canViewTracking(TzOrderPurchaseStatusEnum status) {
        return OrderStatusUtil.canViewTracking(status);
    }

    // ==================== 价格计算方法 ====================

    /**
     * 计算商品美元价格
     *
     * @param price        人民币价格
     * @param exchangeRate 汇率
     * @return 美元价格
     */
    default BigDecimal calculateItemUsdPrice(BigDecimal price, BigDecimal exchangeRate) {
        if (price == null) {
            return BigDecimal.ZERO;
        }
        if (exchangeRate == null || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            // 使用默认汇率 1 CNY = 0.14 USD
            exchangeRate = new BigDecimal("0.14");
        }
        return price.multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算订单总美元金额
     *
     * @param totalAmount  人民币总金额
     * @param exchangeRate 汇率
     * @return 美元总金额
     */
    default BigDecimal calculateTotalAmountUsd(BigDecimal totalAmount, BigDecimal exchangeRate) {
        return calculateItemUsdPrice(totalAmount, exchangeRate);
    }

    /**
     * 转换订单详情
     * 
     * @param purchaseOrder 采购订单
     * @param orderItems    订单项列表
     * @return 前端订单详情
     */
    UserOrderDetailVO convertToUserOrderDetailVO(TzOrderPurchase purchaseOrder, List<TzOrderItem> orderItems);
}
