2025-07-10 00:24:04 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=53m12s841ms).
2025-07-10 01:10:55 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1129178491 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6471fceb, L:/*************:52050 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 56, 49, 48, 53, 48, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1129178491 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6471fceb, L:/*************:52050 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 56, 49, 48, 53, 48, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1129178491 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6471fceb, L:/*************:52050 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 56, 49, 48, 53, 48, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 01:28:21 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h4m16s833ms).
2025-07-10 02:15:47 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47m25s926ms).
2025-07-10 02:32:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m3s113ms).
2025-07-10 02:32:51 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1556401971 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xfda8b159, L:/*************:52059 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 56, 53, 57, 54, 54, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1556401971 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xfda8b159, L:/*************:52059 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 56, 53, 57, 54, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1556401971 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xfda8b159, L:/*************:52059 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 56, 53, 57, 54, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 03:23:24 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=50m34s482ms).
2025-07-10 04:46:28 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h23m3s297ms).
2025-07-10 05:02:56 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 57, 52, 57, 55, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 57, 52, 57, 55, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 48, 57, 52, 57, 55, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 06:24:08 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h37m39s872ms).
2025-07-10 07:20:38 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=56m30s770ms).
2025-07-10 07:44:03 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@898368162 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ceb2d8e, L:/*************:52038 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 52, 49, 54, 50, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@898368162 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ceb2d8e, L:/*************:52038 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 52, 49, 54, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@898368162 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ceb2d8e, L:/*************:52038 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 52, 49, 54, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 07:44:23 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=23m44s706ms).
2025-07-10 07:57:36 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1601211078 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x1fb4d7dd, L:/*************:52036 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 52, 55, 48, 51, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1601211078 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x1fb4d7dd, L:/*************:52036 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 52, 55, 48, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1601211078 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x1fb4d7dd, L:/*************:52036 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 52, 55, 48, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:00:08 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m14s597ms).
2025-07-10 08:00:51 ERROR [XNIO-1 task-4] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/home/<USER>
jakarta.servlet.ServletException: Request processing failed: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:00:51 ERROR [XNIO-1 task-3] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/home/<USER>
jakarta.servlet.ServletException: Request processing failed: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 1, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1601211078 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x1fb4d7dd, L:/*************:52036 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 1, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1601211078 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x1fb4d7dd, L:/*************:52036 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:00:51 ERROR [XNIO-1 task-4] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$0(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-10 08:00:51 ERROR [XNIO-1 task-3] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$0(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-10 08:00:56 ERROR [XNIO-1 task-5] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/users/shopping-cart
jakarta.servlet.ServletException: Request processing failed: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1129178491 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6471fceb, L:/*************:52050 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1129178491 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6471fceb, L:/*************:52050 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:00:56 ERROR [XNIO-1 task-5] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$0(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-10 08:00:56 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005023: Exception handling request to /api/home/<USER>
jakarta.servlet.ServletException: Request processing failed: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:122)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:103)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (GET), params: [Authorization:login:token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MTk5NDMyOTY2MjgyNzcyNTAsInJuU3RyIjoicDRxclQ0bE1MMHhLMkl3R0t3VkFZV1h4MDNsaFM1VWwifQ.Ps8nFYnujj1f8uwOrmMGro0d0Bb-fTnkQB1ENe6kbkM] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:00:56 ERROR [XNIO-1 task-2] [tid::uId::ip::os::browser:] io.undertow.request - UT005022: Exception generating error page /error
jakarta.servlet.ServletException: Request processing failed: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1022)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:258)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchToPath(ServletInitialHandler.java:183)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:415)
	at io.undertow.servlet.spec.RequestDispatcherImpl.error(RequestDispatcherImpl.java:373)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:315)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: cn.dev33.satoken.exception.SaTokenContextException: SaTokenContext 上下文尚未初始化
	at cn.dev33.satoken.context.SaTokenContextForThreadLocalStaff.getModelBox(SaTokenContextForThreadLocalStaff.java:73)
	at cn.dev33.satoken.context.SaTokenContextForThreadLocal.getModelBox(SaTokenContextForThreadLocal.java:55)
	at cn.dev33.satoken.context.SaTokenContext.getRequest(SaTokenContext.java:66)
	at cn.dev33.satoken.context.SaHolder.getRequest(SaHolder.java:49)
	at cn.dev33.satoken.router.SaRouter.isMatchCurrURI(SaRouter.java:141)
	at cn.dev33.satoken.router.SaRouterStaff.match(SaRouterStaff.java:74)
	at cn.dev33.satoken.router.SaRouter.match(SaRouter.java:172)
	at com.fulfillmen.starter.auth.satoken.autoconfigure.SaTokenAutoConfiguration.lambda$0(SaTokenAutoConfiguration.java:66)
	at cn.dev33.satoken.interceptor.SaInterceptor.preHandle(SaInterceptor.java:102)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	... 50 common frames omitted
2025-07-10 08:00:56 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=48s462ms).
2025-07-10 08:00:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-09 22:00:00,477 to 2025-07-10 08:00:56,786
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 08:01:16 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 54, 55, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 54, 55, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 54, 55, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:02:27 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 55, 52, 51, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 55, 52, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 55, 52, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:04:01 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 56, 51, 54, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 56, 51, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 56, 51, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:04:02 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=58s383ms).
2025-07-10 08:06:45 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 57, 48, 51, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 57, 48, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 53, 57, 48, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:07:11 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m7s345ms).
2025-07-10 08:08:51 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m39s367ms).
2025-07-10 08:09:00 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 49, 51, 53, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 49, 51, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 49, 51, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:11:39 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m17s349ms).
2025-07-10 08:12:41 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 51, 53, 54, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 51, 53, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 51, 53, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:12:58 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m18s655ms).
2025-07-10 08:14:42 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 52, 55, 56, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 52, 55, 56, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 52, 55, 56, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:14:54 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m25s760ms).
2025-07-10 08:15:57 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m3s330ms).
2025-07-10 08:16:43 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 53, 55, 55, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 53, 55, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 53, 55, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:16:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=52s341ms).
2025-07-10 08:19:56 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m5s851ms).
2025-07-10 08:20:24 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 56, 49, 57, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 56, 49, 57, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 56, 49, 57, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:21:30 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 56, 56, 53, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 56, 56, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 56, 56, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:22:37 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 57, 53, 50, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 57, 53, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 54, 57, 53, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:24:27 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m58s34ms).
2025-07-10 08:25:10 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 49, 48, 53, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 49, 48, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 49, 48, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:26:17 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 49, 55, 50, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 49, 55, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 49, 55, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:27:23 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 50, 51, 56, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 50, 51, 56, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 50, 51, 56, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:28:02 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 08:00:56,786 to 2025-07-10 08:28:02,325
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 08:28:55 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 51, 48, 52, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 51, 48, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 51, 48, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:28:59 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=56s568ms).
2025-07-10 08:30:02 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 51, 57, 55, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 51, 57, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 51, 57, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:32:52 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m21s338ms).
2025-07-10 08:32:58 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 53, 55, 52, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 53, 55, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 53, 55, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:34:05 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 54, 52, 48, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 54, 52, 48, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 54, 52, 48, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:34:59 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m5s470ms).
2025-07-10 08:36:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m51s964ms).
2025-07-10 08:37:57 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 56, 55, 51, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 56, 55, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 55, 56, 55, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:39:58 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m7s38ms).
2025-07-10 08:42:20 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m22s349ms).
2025-07-10 08:42:41 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 49, 53, 54, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 49, 53, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 49, 53, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:43:48 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 50, 50, 51, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 50, 50, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 50, 50, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:45:09 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m16s145ms).
2025-07-10 08:48:35 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m26s131ms).
2025-07-10 08:48:35 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 52, 56, 57, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 52, 56, 57, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 52, 56, 57, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:49:55 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 53, 57, 48, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 53, 57, 48, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 53, 57, 48, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:52:08 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m17s805ms).
2025-07-10 08:52:48 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 55, 54, 52, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 55, 54, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 55, 54, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:55:11 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m556ms).
2025-07-10 08:55:25 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 57, 50, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 57, 50, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 57, 50, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:56:32 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 57, 56, 55, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 57, 56, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 56, 57, 56, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 08:57:25 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m12s734ms).
2025-07-10 08:59:31 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 49, 54, 54, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 49, 54, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 49, 54, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:00:24 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m29s312ms).
2025-07-10 09:04:27 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m2s754ms).
2025-07-10 09:07:48 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 54, 54, 51, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 54, 54, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 54, 54, 51, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:07:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m22s263ms).
2025-07-10 09:08:55 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 08:28:02,325 to 2025-07-10 09:08:55,604
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 09:08:55 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m5s913ms).
2025-07-10 09:09:30 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 55, 54, 53, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 55, 54, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 55, 54, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:10:37 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 56, 51, 50, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 56, 51, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 56, 51, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:11:56 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 57, 49, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 57, 49, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 57, 49, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:13:03 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 57, 55, 56, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 57, 55, 56, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 48, 57, 57, 55, 56, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:14:40 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m25s399ms).
2025-07-10 09:15:30 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 49, 50, 53, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 49, 50, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 49, 50, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:15:38 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=57s720ms).
2025-07-10 09:18:04 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 49, 57, 50, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 49, 57, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 49, 57, 50, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:18:06 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m57s872ms).
2025-07-10 09:19:11 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 51, 52, 54, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 51, 52, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 51, 52, 54, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:22:29 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m49s901ms).
2025-07-10 09:22:36 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 53, 53, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 53, 53, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1462777794 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xf971bee9, L:/*************:52056 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 53, 53, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:24:47 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m18s207ms).
2025-07-10 09:25:29 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 55, 50, 52, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 55, 50, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@1898937810 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x6fc0af60, L:/*************:52054 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 55, 50, 52, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:26:58 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m11s259ms).
2025-07-10 09:27:15 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 56, 51, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 56, 51, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@187334765 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x9ab25567, L:/*************:52058 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 56, 51, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:28:22 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 56, 57, 55, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 56, 57, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@226334796 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0x26d76f5a, L:/*************:52030 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 56, 57, 55, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:29:29 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 57, 54, 53, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 57, 54, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@2143922749 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xd737e310, L:/*************:52048 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 48, 57, 54, 53, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:30:37 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m4s158ms).
2025-07-10 09:32:12 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m34s829ms).
2025-07-10 09:32:13 ERROR [MachineIdGuarder] [tid::uId::ip::os::browser:] me.ahoo.cosid.machine.DefaultMachineIdGuarder - Guard Failed:[Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 49, 49, 49, 49, ...]] after 3 of 3 retry attempts]!
org.springframework.data.redis.RedisConnectionFailureException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 49, 49, 49, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:40)
	at org.redisson.spring.data.connection.RedissonExceptionConverter.convert(RedissonExceptionConverter.java:35)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)
	at org.redisson.spring.data.connection.RedissonConnection.transform(RedissonConnection.java:208)
	at org.redisson.spring.data.connection.RedissonConnection.syncFuture(RedissonConnection.java:203)
	at org.redisson.spring.data.connection.RedissonConnection.sync(RedissonConnection.java:374)
	at org.redisson.spring.data.connection.RedissonConnection.write(RedissonConnection.java:740)
	at org.redisson.spring.data.connection.RedissonConnection.evalSha(RedissonConnection.java:1893)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.evalSha(DefaultStringRedisConnection.java:1351)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.data.redis.core.CloseSuppressingInvocationHandler.invoke(CloseSuppressingInvocationHandler.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy204.evalSha(Unknown Source)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.eval(DefaultScriptExecutor.java:77)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.lambda$execute$0(DefaultScriptExecutor.java:68)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:411)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:378)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:365)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:58)
	at org.springframework.data.redis.core.script.DefaultScriptExecutor.execute(DefaultScriptExecutor.java:52)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:508)
	at me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor.guardRemote(SpringRedisMachineIdDistributor.java:133)
	at me.ahoo.cosid.machine.AbstractMachineIdDistributor.guard(AbstractMachineIdDistributor.java:92)
	at me.ahoo.cosid.machine.DefaultMachineIdGuarder.safeGuard(DefaultMachineIdGuarder.java:101)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:358)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.redisson.client.WriteRedisConnectionException: Unable to write command into connection! Check CPU usage of the JVM. Try to increase nettyThreads setting. Netty pending tasks: 0, Node source: NodeSource [slot=0, addr=null, redisClient=null, redirect=null, entry=null], connection: RedisConnection@85105075 [redisClient=[addr=redis://************:6379,************/************:6379], channel=[id: 0xe2757f11, L:/*************:52040 ! R:************/************:6379], currentCommand=null, usage=1], command: (EVALSHA), params: [d3f3370f1163c867dbeea875b97779b29266f0fe, 1, [123, 102, 117, 108, 102, 105, 108, 108, 109, 101, ...], [49, 57, 50, 46, 49, 54, 56, 46, 51, 51, ...], [49, 55, 53, 50, 49, 49, 49, 49, 49, 49, ...]] after 3 of 3 retry attempts
	at org.redisson.command.RedisExecutor.checkWriteFuture(RedisExecutor.java:380)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:202)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setFailure0(DefaultPromise.java:629)
	at io.netty.util.concurrent.DefaultPromise.tryFailure(DefaultPromise.java:118)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.safeSetFailure(AbstractChannel.java:997)
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(AbstractChannel.java:858)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.write(DefaultChannelPipeline.java:1314)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWrite0(AbstractChannelHandlerContext.java:889)
	at io.netty.channel.AbstractChannelHandlerContext.invokeWriteAndFlush(AbstractChannelHandlerContext.java:956)
	at io.netty.channel.AbstractChannelHandlerContext$WriteTask.run(AbstractChannelHandlerContext.java:1263)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
Caused by: io.netty.channel.StacklessClosedChannelException: null
	at io.netty.channel.AbstractChannel$AbstractUnsafe.write(Object, ChannelPromise)(Unknown Source)
2025-07-10 09:33:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 09:08:55,604 to 2025-07-10 09:33:56,626
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 09:48:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 09:33:56,626 to 2025-07-10 09:48:56,627
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 10:03:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 09:48:56,627 to 2025-07-10 10:03:56,629
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 10:09:34 ERROR [redisson-netty-1-18] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x0c53f779, L:/**************:55664 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:34 ERROR [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x6448858e, L:/**************:55665 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:34 ERROR [redisson-netty-1-20] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x72038913, L:/**************:55669 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:34 ERROR [redisson-netty-1-21] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x4a0a89cf, L:/**************:55670 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-22] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x37147e6f, L:/**************:55671 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-23] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x60f06a4b, L:/**************:55678 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.buffer.WrappedByteBuf.writeBytes(WrappedByteBuf.java:821)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-24] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x83ea8967, L:/**************:55681 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-26] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xde2b5c8c, L:/**************:55683 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-27] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x0520aa72, L:/**************:55684 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-29] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x46130b54, L:/**************:55685 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-30] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x93417940, L:/**************:55687 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-1] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xa025cc0d, L:/**************:55690 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-32] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x208ae803, L:/**************:55689 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-2] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x663b32cf, L:/**************:55692 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:35 ERROR [redisson-netty-1-3] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x07be0a53, L:/**************:55691 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:36 ERROR [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xe0c59d73, L:/**************:55696 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:36 ERROR [redisson-netty-1-6] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x2ecf64b3, L:/**************:55697 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:36 ERROR [redisson-netty-1-7] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x496496d0, L:/**************:55698 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:36 ERROR [redisson-netty-1-8] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0xb72ad9a3, L:/**************:55703 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:41 ERROR [redisson-netty-1-31] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x864574a6, L:/**************:55688 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:45 ERROR [redisson-netty-1-28] [tid::uId::ip::os::browser:] org.redisson.client.handler.ErrorsLoggingHandler - Exception occured. Channel: [id: 0x3dbe103d, L:/**************:55686 - R:************/************:6379]
java.io.IOException: Operation timed out
	at java.base/sun.nio.ch.SocketDispatcher.read0(Native Method)
	at java.base/sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:47)
	at java.base/sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:340)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:294)
	at java.base/sun.nio.ch.IOUtil.read(IOUtil.java:269)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:425)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:796)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:732)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:46 ERROR [redisson-timer-4-1] [tid::uId::ip::os::browser:] org.redisson.client.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xdcb84e13, L:/**************:55662 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379,************/************:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:46 ERROR [redisson-timer-4-1] [tid::uId::ip::os::browser:] org.redisson.client.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xc97ee855, L:/**************:55663 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379,************/************:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:47 ERROR [redisson-timer-4-1] [tid::uId::ip::os::browser:] org.redisson.client.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xb9d0ef60, L:/**************:55682 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379,************/************:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:09:48 ERROR [redisson-timer-4-1] [tid::uId::ip::os::browser:] org.redisson.client.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x12ec9fd8, L:/**************:55695 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379,************/************:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-10 10:18:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 10:03:56,629 to 2025-07-10 10:18:56,635
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 10:33:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 10:18:56,635 to 2025-07-10 10:33:56,603
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 10:48:56 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-10 10:33:56,603 to 2025-07-10 10:48:56,597
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
cart:                      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-10 10:50:30 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=0, lastTimeStamp=1752115830718}] instanceId:[InstanceId{instanceId=**************:21344, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-10 10:50:30 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-10 10:50:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-10 10:50:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-10 10:50:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-10 10:50:32 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-10 10:50:34 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-10 10:50:36 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 10:50:36 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 10:50:36 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-10 10:50:36 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-10 10:50:36 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-10 10:50:37 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-10 10:50:37 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-10 10:50:37 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2340 ms
2025-07-10 10:50:37 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-10 10:50:37 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-10 10:50:37 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-10 10:50:38 INFO  [redisson-netty-1-5] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for ************/************:6379
2025-07-10 10:50:38 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for ************/************:6379
2025-07-10 10:50:38 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-10 10:50:38 WARN  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'testGracefulResponseController': Lookup method resolution failed
2025-07-10 10:50:38 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-10 10:50:38 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-10 10:50:38 ERROR [main] [tid::uId::ip::os::browser:] org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'testGracefulResponseController': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:498)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1314)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1209)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1364)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1353)
	at com.fulfillmen.shop.BootstrapApplication.main(BootstrapApplication.java:47)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.fulfillmen.shop.controller.TestGracefulResponseController] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@76ed5528]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:320)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:476)
	... 19 common frames omitted
Caused by: java.lang.NoClassDefFoundError: UserAuthReq/UserLoginDTO
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3578)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2676)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 21 common frames omitted
Caused by: java.lang.ClassNotFoundException: UserAuthReq.UserLoginDTO
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	... 25 common frames omitted
