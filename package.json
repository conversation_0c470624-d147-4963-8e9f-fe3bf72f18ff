{"name": "fulfillmen-shop-vue", "private": true, "version": "0.1.0", "description": "A modern e-commerce platform built with Vue 3, Vite, and Pinia", "type": "module", "scripts": {"dev": "vite", "dev:debug": "cross-env VITE_APP_DEBUG=true vite", "dev:dev": "vite --mode dev", "dev:test": "vite --mode testing", "dev:sealos": "vite --mode sealos", "dev:prod": "vite --mode production", "build": "vite build", "build:dev": "vite build --mode dev", "build:java": "vite build --mode java", "build:test": "vite build --mode testing", "build:sealos": "vite build --mode sealos", "build:prod": "vite build --mode production", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:cart": "vitest run src/stores/__tests__/cart.test.ts src/utils/__tests__/cart*.test.ts src/components/cart/__tests__/*.test.ts"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fontsource/montserrat": "^5.2.5", "@fortawesome/fontawesome-free": "^6.7.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.9.0", "dexie": "^4.0.11", "element-plus": "^2.9.10", "lodash": "^4.17.21", "lucide-vue-next": "^0.475.0", "marked": "^16.0.0", "pinia": "^2.3.1", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-devtools": "^7.7.6", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1"}, "devDependencies": {"@stagewise-plugins/vue": "^0.5.0", "@stagewise/toolbar": "^0.5.0", "@stagewise/toolbar-vue": "^0.5.0", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.18", "@types/marked": "^6.0.0", "@types/node": "^22.15.18", "@types/vue-i18n": "^6.1.3", "@vitejs/plugin-vue": "^4.6.2", "@vitest/coverage-v8": "^3.2.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "jsdom": "^26.1.0", "postcss": "^8.5.3", "sass-embedded": "^1.88.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^5.4.19", "vitest": "^3.2.3", "vue-tsc": "^1.8.27"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}